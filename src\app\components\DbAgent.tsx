"use client";

import { useState } from "react";
import DbConfig from "./DbConfig";
import DbAgentChat from "./DbAgentChat";

export default function DbAgent() {
  const [activeTab, setActiveTab] = useState<'config' | 'chat'>('config');

  return (
    <div className="h-full flex flex-col bg-white rounded-lg shadow-sm">
      {/* Tab 导航 */}
      <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
        <div className="flex space-x-1">
          <button
            onClick={() => setActiveTab('config')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
              activeTab === 'config'
                ? 'bg-blue-50 text-blue-700 border-2 border-blue-200'
                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 border-2 border-transparent'
            }`}
          >
            <div className="flex items-center space-x-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
              </svg>
              <span>DB配置</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab('chat')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
              activeTab === 'chat'
                ? 'bg-blue-50 text-blue-700 border-2 border-blue-200'
                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 border-2 border-transparent'
            }`}
          >
            <div className="flex items-center space-x-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              <span>DB Agent Chat</span>
            </div>
          </button>
        </div>
        
        {/* 状态指示器 */}
        <div className="flex items-center space-x-2 px-3 py-1 bg-purple-50 border border-purple-200 rounded-full">
          <div className="w-2 h-2 rounded-full bg-purple-500"></div>
          <span className="text-xs font-medium text-purple-700">
            DB智能助手
          </span>
        </div>
      </div>

      {/* Tab 内容区域 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {activeTab === 'config' && <DbConfig />}
        {activeTab === 'chat' && <DbAgentChat />}
      </div>
    </div>
  );
} 