// 统一API请求工具
export const BACKEND_API_URL = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://127.0.0.1:5008';

export async function apiGet(path: string, options: RequestInit = {}) {
  const res = await fetch(`${BACKEND_API_URL}${path}`, {
    ...options,
    method: 'GET',
  });
  if (!res.ok) throw new Error(await res.text());
  return res.json();
}

export async function apiPost(path: string, body: unknown, options: RequestInit = {}) {
  const res = await fetch(`${BACKEND_API_URL}${path}`, {
    ...options,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...(options.headers || {}),
    },
    body: JSON.stringify(body),
  });
  if (!res.ok) throw new Error(await res.text());
  return res.json();
}

export async function apiPut(path: string, body: unknown, options: RequestInit = {}) {
  const res = await fetch(`${BACKEND_API_URL}${path}`, {
    ...options,
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      ...(options.headers || {}),
    },
    body: JSON.stringify(body),
  });
  if (!res.ok) throw new Error(await res.text());
  return res.json();
}

export async function apiDelete(path: string, options: RequestInit = {}) {
  const res = await fetch(`${BACKEND_API_URL}${path}`, {
    ...options,
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
      ...(options.headers || {}),
    },
  });
  if (!res.ok) throw new Error(await res.text());
  return res.json();
} 
