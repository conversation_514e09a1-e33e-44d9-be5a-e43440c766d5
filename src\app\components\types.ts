// 定义聊天消息和AI Agent相关类型
export type ChatMessage = {
  role: "user" | "assistant";
  content: string;
};

export type AgentStepData = {
  step: number;
  screenshot?: string;
  output_html: string;
};

export type AgentAssistanceData = {
  query: string;
  request_id: string;
  response?: string;
};

export type AgentDoneData = {
  summary: string;
  success?: boolean;
};

export type AgentTaskStep = {
  step: number;
  action: string;
  content: string;
  timestamp: number;
};

export type AgentMessage = {
  type: 'step';
  data: AgentStepData;
} | {
  type: 'assistance_request';
  data: AgentAssistanceData;
} | {
  type: 'done';
  data: AgentDoneData;
} | {
  type: 'error';
  data: string;
} | {
  type: 'agent_task_step';
  data: AgentTaskStep;
};

export type TaskOutput = {
  gif?: string;
  history?: string;
};

// 报表相关类型
export interface ScoreStats {
  average: number;
  max: number;
  min: number;
  median: number;
  std: number;
}

export interface ComponentStats {
  average: number;
  max: number;
  min: number;
}

export interface BasicStatistics {
  total_students: number;
  final_score_stats: ScoreStats;
  component_stats: {
    regular_scores: ComponentStats;
    homework_scores: ComponentStats;
    practical_scores: ComponentStats;
    exam_scores: ComponentStats;
  };
}

export interface FailingStudent {
  name: string;
  student_id: string;
  final_score: number;
  exam_score: number;
  regular_score: number;
  gap: number;
}

export interface FailAnalysis {
  total_students: number;
  fail_count: number;
  fail_rate: number;
  failing_students: FailingStudent[];
}

export interface ScoreDistributionRange {
  range: string;
  min_score: number;
  max_score: number;
  count: number;
  percentage: number;
}

export interface ScoreDistributionRates {
  excellent_rate: number;
  good_rate: number;
  pass_rate: number;
}

export interface DistributionAnalysis {
  distribution: ScoreDistributionRange[];
  rates: ScoreDistributionRates;
  total_students: number;
}

export interface RankedStudent {
  rank: number;
  student_name: string;
  student_id: string;
  regular_score: number;
  homework_score: number;
  practical_score: number;
  exam_score: number;
  final_score: number;
  status: string;
}

export interface CourseInfo {
  course_name: string;
  class_name: string;
  teacher_name: string;
  total_students: number;
}

export interface ScoreDistributionData {
  success: boolean;
  course_info: CourseInfo;
  distribution_analysis: DistributionAnalysis;
  basic_statistics: BasicStatistics;
}

export interface WarningInfo {
  requires_warning: boolean;
  warning_message: string;
}

export interface StatisticsData {
  course_info: CourseInfo;
  basic_statistics: BasicStatistics;
  fail_analysis: FailAnalysis;
  distribution_analysis: DistributionAnalysis;
  warning_info: WarningInfo;
}

export interface ComprehensiveReport {
  timestamp: string;
  course_info: CourseInfo;
  basic_statistics: BasicStatistics;
  fail_analysis: FailAnalysis;
  distribution_analysis: DistributionAnalysis;
  ranking_table: RankedStudent[];
  charts: { [key: string]: string };
  warning_info: WarningInfo;
  report_summary: string;
}

// Action相关类型
export interface DoneAction {
  done: {
    text: string;
    success: boolean;
  };
}

export interface OtherAction {
  [key: string]: unknown; // 允许其他类型的action
  done?: never; // 确保OtherAction不会有done属性
}

export type AgentAction = DoneAction | OtherAction;

// 导航类型
export type ActiveTab = 'agent-chat' | 'agent-history' | 'agent-reports';