import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: '************',
        port: '5008',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: '************',
        port: '5008',
        pathname: '/**',
      }
    ],
    // 允许data URLs (base64图片)
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
  },
};

export default nextConfig;
