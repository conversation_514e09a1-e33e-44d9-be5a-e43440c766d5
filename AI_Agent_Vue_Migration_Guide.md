# AI Agent 智能助手 Vue 移植技术文档

## 📋 项目概述

AI Agent 智能助手是一个基于浏览器自动化的智能任务执行系统，支持自然语言指令转换为具体的浏览器操作，并提供实时反馈和交互功能。

## 👤 用户使用流程

### 基本使用流程
1. **输入任务**: 用户在文本框中输入自然语言任务描述
   - 例如："帮我在百度搜索'Vue 3教程'并打开第一个结果"
   - 例如："登录我的邮箱并查看最新邮件"
   - 例如："在淘宝搜索'手机'并筛选价格1000-2000元的商品"

2. **任务执行**: 点击"执行任务"按钮，AI Agent开始工作
   - 系统显示"正在执行"状态
   - 实时显示Agent的执行步骤
   - 每个步骤都有截图反馈

3. **实时反馈**: 用户可以看到Agent的每一步操作
   - 步骤编号和描述
   - 当前页面的截图
   - Agent的思考过程和下一步计划

4. **协助交互**: 当Agent遇到问题时会请求用户帮助
   - Agent询问具体问题（如验证码、选择项等）
   - 用户输入回复
   - Agent根据回复继续执行

5. **任务完成**: Agent完成任务后显示总结
   - 成功/失败状态
   - 任务执行摘要
   - 可选的结果文件（截图、数据等）

### 高级使用场景
- **文件上传任务**: 涉及Excel处理、图片上传等
- **多步骤复杂任务**: 需要在多个网站间操作
- **数据采集任务**: 自动抓取网页数据并整理
- **表单填写任务**: 批量填写在线表单

## 🔄 完整API调用流程

### 流程图概览
```
用户输入任务 → 发送POST请求 → 建立SSE连接 → 接收实时事件 → 处理用户交互 → 任务完成
```

### 详细调用流程

#### 1. 任务启动阶段
```typescript
// 步骤1: 用户点击"执行任务"
const startTask = async () => {
  // 构建请求体
  const requestBody = {
    task: "用户输入的任务描述",
    settings: {
      llm_provider: "deepseek",
      llm_model_name: "deepseek-chat",
      // ... 其他配置
    }
  }

  // 步骤2: 发送POST请求到后端
  const response = await fetch('/api/agent/run', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(requestBody)
  })

  // 步骤3: 建立SSE连接，开始接收实时数据
  const reader = response.body.getReader()
  // ... SSE处理逻辑
}
```

#### 2. SSE事件接收阶段
```typescript
// 持续接收后端发送的事件
while (true) {
  const { done, value } = await reader.read()
  if (done) break

  // 解析SSE数据
  const chunk = decoder.decode(value, { stream: true })
  const lines = chunk.split('\n\n')

  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const eventData = JSON.parse(line.substring(6))

      // 根据事件类型处理
      switch (eventData.type) {
        case 'step':
          // 显示执行步骤和截图
          break
        case 'assistance_request':
          // 显示协助请求，等待用户回复
          break
        case 'done':
          // 任务完成，显示结果
          break
        case 'error':
          // 处理错误
          break
      }
    }
  }
}
```

#### 3. 用户协助交互阶段
```typescript
// 当收到assistance_request事件时
const handleAssistanceRequest = (eventData) => {
  // 显示Agent的问题
  const query = eventData.data.query
  const requestId = eventData.data.request_id

  // 等待用户输入回复
  // 用户输入完成后，发送回复
  const sendResponse = async (userResponse) => {
    await fetch('/api/agent/respond', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        task_id: requestId,
        response: userResponse
      })
    })
    // Agent会通过原有SSE连接继续发送更新
  }
}
```

#### 4. 任务完成阶段
```typescript
// 当收到done事件时
const handleTaskComplete = (eventData) => {
  const { summary, success } = eventData.data

  // 显示完成状态
  if (success) {
    showSuccessMessage(summary)
  } else {
    showErrorMessage(summary)
  }

  // 清理状态
  isLoading.value = false
  currentTaskId.value = null
}
```

### API调用时序图
```
前端                    后端                    AI Agent
 |                       |                        |
 |-- POST /api/agent/run -->                      |
 |                       |-- 启动Agent任务 ------>|
 |<-- SSE连接建立 -------|                        |
 |                       |<-- step事件 -----------|
 |<-- step事件 ----------|                        |
 |                       |<-- assistance_request -|
 |<-- assistance_request |                        |
 |                       |                        |
 |-- POST /api/agent/respond -->                  |
 |                       |-- 发送用户回复 ------->|
 |                       |<-- step事件 -----------|
 |<-- step事件 ----------|                        |
 |                       |<-- done事件 -----------|
 |<-- done事件 ----------|                        |
 |-- 关闭SSE连接 ------->|                        |
```

### 常见使用场景和API调用示例

#### 场景1: 简单搜索任务
```typescript
// 用户输入: "在百度搜索Vue 3教程"
const taskRequest = {
  task: "在百度搜索Vue 3教程",
  settings: {
    llm_provider: "deepseek",
    llm_model_name: "deepseek-chat",
    llm_temperature: 0.6,
    use_vision: true,
    max_steps: 50,
    headless: false,
    window_w: 1920,
    window_h: 1080
  }
}

// 预期的SSE事件流:
// 1. step事件: "正在打开百度首页"
// 2. step事件: "正在搜索框中输入'Vue 3教程'"
// 3. step事件: "点击搜索按钮"
// 4. step事件: "搜索结果已显示"
// 5. done事件: "任务完成，已成功搜索Vue 3教程"
```

#### 场景2: 需要用户协助的任务
```typescript
// 用户输入: "登录我的Gmail邮箱"
const taskRequest = {
  task: "登录我的Gmail邮箱",
  settings: { /* 配置 */ }
}

// 预期的SSE事件流:
// 1. step事件: "正在打开Gmail登录页面"
// 2. assistance_request事件: "请提供您的邮箱地址"
//    用户回复: "<EMAIL>"
// 3. step事件: "正在输入邮箱地址"
// 4. assistance_request事件: "请提供您的密码"
//    用户回复: "password123"
// 5. step事件: "正在输入密码并登录"
// 6. assistance_request事件: "检测到验证码，请输入验证码: [截图]"
//    用户回复: "ABCD"
// 7. step事件: "正在输入验证码"
// 8. done事件: "登录成功"
```

#### 场景3: 复杂的数据采集任务
```typescript
// 用户输入: "采集淘宝手机商品前10页的价格信息"
const taskRequest = {
  task: "采集淘宝手机商品前10页的价格信息",
  settings: {
    max_steps: 200,  // 复杂任务需要更多步骤
    /* 其他配置 */
  }
}

// 预期的SSE事件流:
// 1. step事件: "正在打开淘宝首页"
// 2. step事件: "正在搜索'手机'"
// 3. step事件: "正在采集第1页商品信息"
// 4. step事件: "已采集20个商品，正在翻页"
// 5. step事件: "正在采集第2页商品信息"
// ... (重复采集过程)
// N. done事件: "任务完成，共采集200个商品信息"
```

### 错误处理和边界情况

#### 1. 网络连接错误
```typescript
const handleNetworkError = () => {
  try {
    // SSE连接处理
  } catch (error) {
    if (error.name === 'NetworkError') {
      showErrorMessage('网络连接失败，请检查网络设置')
      // 提供重试选项
      showRetryButton()
    }
  }
}
```

#### 2. 任务超时处理
```typescript
// 设置任务超时（例如30分钟）
const TASK_TIMEOUT = 30 * 60 * 1000

const taskTimeoutHandler = setTimeout(() => {
  if (isLoading.value) {
    handleAbortTask()
    showErrorMessage('任务执行超时，已自动取消')
  }
}, TASK_TIMEOUT)
```

#### 3. Agent执行错误
```typescript
// 当收到error事件时
const handleAgentError = (eventData) => {
  const errorMessage = eventData.data

  // 根据错误类型提供不同的处理方案
  if (errorMessage.includes('页面加载失败')) {
    showErrorMessage('页面加载失败，可能是网络问题或网站不可访问')
    // 提供重试或更换网站的选项
  } else if (errorMessage.includes('元素未找到')) {
    showErrorMessage('页面结构可能已变化，Agent无法找到目标元素')
    // 建议用户重新描述任务或提供更多信息
  } else {
    showErrorMessage(`执行出错: ${errorMessage}`)
  }
}
```

#### 4. 用户取消任务
```typescript
const handleUserCancel = () => {
  // 发送取消信号
  if (abortController) {
    abortController.abort()
  }

  // 清理状态
  isLoading.value = false
  isWaitingForHelp.value = false
  currentTaskId.value = null

  // 添加取消消息到聊天历史
  chatHistory.value.push({
    role: 'assistant',
    content: '任务已被用户取消'
  })
}
```

### 性能优化建议

#### 1. 大量消息处理
```typescript
// 当聊天历史过多时，只保留最近的消息
const MAX_CHAT_HISTORY = 100

const addMessageToHistory = (message) => {
  chatHistory.value.push(message)

  // 保持历史记录在合理范围内
  if (chatHistory.value.length > MAX_CHAT_HISTORY) {
    chatHistory.value = chatHistory.value.slice(-MAX_CHAT_HISTORY)
  }
}
```

#### 2. 截图优化
```typescript
// 压缩大尺寸截图
const optimizeScreenshot = (base64Image) => {
  const img = new Image()
  img.onload = () => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    // 如果图片太大，进行压缩
    const maxWidth = 800
    const maxHeight = 600

    let { width, height } = img
    if (width > maxWidth || height > maxHeight) {
      const ratio = Math.min(maxWidth / width, maxHeight / height)
      width *= ratio
      height *= ratio
    }

    canvas.width = width
    canvas.height = height
    ctx.drawImage(img, 0, 0, width, height)

    return canvas.toDataURL('image/jpeg', 0.8)
  }
  img.src = `data:image/png;base64,${base64Image}`
}
```

### 状态管理详解

#### 应用状态流转
```typescript
// 状态枚举
enum AgentState {
  IDLE = 'idle',           // 空闲状态
  LOADING = 'loading',     // 执行中
  WAITING_HELP = 'waiting_help',  // 等待用户协助
  COMPLETED = 'completed', // 已完成
  ERROR = 'error',         // 错误状态
  CANCELLED = 'cancelled'  // 已取消
}

// 状态转换逻辑
const stateTransitions = {
  [AgentState.IDLE]: [AgentState.LOADING],
  [AgentState.LOADING]: [AgentState.WAITING_HELP, AgentState.COMPLETED, AgentState.ERROR, AgentState.CANCELLED],
  [AgentState.WAITING_HELP]: [AgentState.LOADING, AgentState.CANCELLED],
  [AgentState.COMPLETED]: [AgentState.IDLE],
  [AgentState.ERROR]: [AgentState.IDLE],
  [AgentState.CANCELLED]: [AgentState.IDLE]
}
```

#### 用户界面状态同步
```typescript
// 根据状态更新UI
const updateUIState = (newState: AgentState) => {
  switch (newState) {
    case AgentState.IDLE:
      isLoading.value = false
      isWaitingForHelp.value = false
      showInputArea.value = true
      inputPlaceholder.value = "请描述您希望AI Agent执行的任务..."
      break

    case AgentState.LOADING:
      isLoading.value = true
      isWaitingForHelp.value = false
      showInputArea.value = false
      statusText.value = "正在执行任务..."
      break

    case AgentState.WAITING_HELP:
      isLoading.value = false
      isWaitingForHelp.value = true
      showInputArea.value = true
      inputPlaceholder.value = "请输入您的回复..."
      statusText.value = "等待您的协助"
      break

    case AgentState.COMPLETED:
      isLoading.value = false
      isWaitingForHelp.value = false
      showInputArea.value = true
      statusText.value = "任务已完成"
      // 3秒后自动回到空闲状态
      setTimeout(() => updateUIState(AgentState.IDLE), 3000)
      break
  }
}
```

### 用户体验优化

#### 1. 加载状态指示
```vue
<template>
  <!-- 全局加载遮罩 -->
  <div v-if="isLoading" class="loading-overlay">
    <div class="loading-spinner">
      <div class="spinner"></div>
      <p>{{ loadingMessage }}</p>
      <el-progress
        v-if="taskProgress > 0"
        :percentage="taskProgress"
        :show-text="false"
      />
    </div>
  </div>

  <!-- 步骤进度指示 -->
  <div class="step-progress">
    <div class="progress-bar">
      <div
        class="progress-fill"
        :style="{ width: `${stepProgress}%` }"
      ></div>
    </div>
    <span class="step-text">步骤 {{ currentStep }} / {{ totalSteps }}</span>
  </div>
</template>
```

#### 2. 实时反馈优化
```typescript
// 打字机效果显示消息
const typewriterEffect = (text: string, element: HTMLElement) => {
  let index = 0
  const speed = 50 // 打字速度

  const type = () => {
    if (index < text.length) {
      element.textContent += text.charAt(index)
      index++
      setTimeout(type, speed)
    }
  }

  element.textContent = ''
  type()
}

// 消息到达动画
const animateNewMessage = (messageElement: HTMLElement) => {
  messageElement.style.opacity = '0'
  messageElement.style.transform = 'translateY(20px)'

  requestAnimationFrame(() => {
    messageElement.style.transition = 'all 0.3s ease'
    messageElement.style.opacity = '1'
    messageElement.style.transform = 'translateY(0)'
  })
}
```

#### 3. 错误恢复机制
```typescript
// 自动重试机制
const autoRetry = async (taskFn: Function, maxRetries = 3) => {
  let retries = 0

  while (retries < maxRetries) {
    try {
      return await taskFn()
    } catch (error) {
      retries++

      if (retries >= maxRetries) {
        throw error
      }

      // 指数退避重试
      const delay = Math.pow(2, retries) * 1000
      await new Promise(resolve => setTimeout(resolve, delay))

      showRetryMessage(`重试中... (${retries}/${maxRetries})`)
    }
  }
}

// 断线重连
const reconnectSSE = async () => {
  if (currentTaskId.value) {
    showMessage('检测到连接中断，正在重新连接...')

    try {
      // 重新建立SSE连接
      await handleAgentTask(lastRequestBody, handleSSEMessage, handleSSEError)
      showMessage('连接已恢复')
    } catch (error) {
      showErrorMessage('重连失败，请手动重试任务')
    }
  }
}
```

#### 4. 用户引导和帮助
```vue
<template>
  <!-- 新手引导 -->
  <div v-if="showGuide" class="user-guide">
    <div class="guide-step" v-for="(step, index) in guideSteps" :key="index">
      <div class="step-number">{{ index + 1 }}</div>
      <div class="step-content">
        <h4>{{ step.title }}</h4>
        <p>{{ step.description }}</p>
        <div v-if="step.example" class="step-example">
          <strong>示例:</strong> {{ step.example }}
        </div>
      </div>
    </div>
  </div>

  <!-- 任务建议 -->
  <div class="task-suggestions">
    <h4>常用任务模板</h4>
    <div class="suggestion-grid">
      <div
        v-for="suggestion in taskSuggestions"
        :key="suggestion.id"
        class="suggestion-card"
        @click="fillTaskTemplate(suggestion.template)"
      >
        <div class="suggestion-icon">{{ suggestion.icon }}</div>
        <div class="suggestion-title">{{ suggestion.title }}</div>
        <div class="suggestion-desc">{{ suggestion.description }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const taskSuggestions = [
  {
    id: 1,
    icon: '🔍',
    title: '网页搜索',
    description: '在搜索引擎中查找信息',
    template: '在百度搜索"{关键词}"并打开第一个结果'
  },
  {
    id: 2,
    icon: '📧',
    title: '邮箱操作',
    description: '登录邮箱查看邮件',
    template: '登录我的{邮箱服务商}邮箱并查看最新邮件'
  },
  {
    id: 3,
    icon: '🛒',
    title: '购物搜索',
    description: '在电商网站搜索商品',
    template: '在{电商网站}搜索"{商品名称}"并筛选价格{价格范围}的商品'
  },
  {
    id: 4,
    icon: '📊',
    title: '数据采集',
    description: '采集网页数据',
    template: '采集{网站名称}的{数据类型}信息，保存为Excel文件'
  }
]

const fillTaskTemplate = (template: string) => {
  task.value = template
  // 高亮模板中的占位符，提示用户修改
  highlightPlaceholders()
}
</script>
```

## 🏗️ 核心架构

### 技术栈要求
- **前端**: Vue 3 + TypeScript + Composition API
- **HTTP客户端**: Axios 或 Fetch API
- **实时通信**: Server-Sent Events (SSE)
- **UI框架**: Element Plus / Ant Design Vue / Naive UI

### 后端API地址
```typescript
const BACKEND_API_URL = process.env.VUE_APP_BACKEND_API_URL || 'http://127.0.0.1:5008'
```

## 📡 API 接口详细说明

### 1. 启动 AI Agent 任务
**接口**: `POST /api/agent/run`

**请求体**:
```typescript
interface AgentRunRequest {
  task: string;  // 用户输入的任务描述
  settings: {
    // LLM 配置
    llm_provider: "deepseek" | "alibaba" | "openai";
    llm_model_name: string;  // 如: "deepseek-chat", "qwen-max-latest"
    llm_temperature: number;  // 0.0-1.0
    use_vision: boolean;
    max_steps: number;
    max_actions_per_step: number;
    max_input_tokens: number;
    
    // 浏览器配置
    headless: boolean;  // false 启用截图功能
    save_agent_history_path: string;
    save_recording_path: string;
    save_trace_path: string;
    save_download_path: string;
    window_w: number;  // 浏览器窗口宽度
    window_h: number;  // 浏览器窗口高度
    keep_browser_open: boolean;
    disable_security: boolean;
  };
}
```

**响应**: Server-Sent Events (SSE) 流式数据

### 2. 响应 Agent 协助请求
**接口**: `POST /api/agent/respond`

**请求体**:
```typescript
interface AgentRespondRequest {
  task_id: string;    // 当前任务ID
  response: string;   // 用户的回复内容
}
```

**响应**: `200 OK` (无返回体，Agent会通过SSE继续发送更新)

## 🔄 SSE 事件类型详解

### 事件格式
```
data: {"type": "事件类型", "data": {...}}
```

### 1. step 事件 - 执行步骤
```typescript
interface StepEvent {
  type: 'step';
  data: {
    step: number;           // 步骤编号
    screenshot?: string;    // Base64编码的截图
    output_html: string;    // 步骤输出HTML内容
  };
}
```

### 2. assistance_request 事件 - 需要用户协助
```typescript
interface AssistanceRequestEvent {
  type: 'assistance_request';
  data: {
    query: string;          // Agent的询问内容
    request_id: string;     // 请求ID，用于回复
    response?: string;      // 用户的回复（可选）
  };
}
```

### 3. done 事件 - 任务完成
```typescript
interface DoneEvent {
  type: 'done';
  data: {
    summary: string;        // 任务完成总结
    success?: boolean;      // 是否成功完成
  };
}
```

### 4. error 事件 - 错误信息
```typescript
interface ErrorEvent {
  type: 'error';
  data: string;             // 错误描述
}
```

### 5. agent_task_step 事件 - 任务步骤记录
```typescript
interface AgentTaskStepEvent {
  type: 'agent_task_step';
  data: {
    step: number;
    action: string;         // 执行的动作
    content: string;        // 动作内容
    timestamp: number;      // 时间戳
  };
}
```

## 💻 Vue 组件实现指南

### 1. 核心状态管理
```typescript
// composables/useAgentChat.ts
import { ref, reactive } from 'vue'

export interface ChatMessage {
  role: 'user' | 'assistant'
  content: string
}

export interface AgentMessage {
  type: 'step' | 'assistance_request' | 'done' | 'error' | 'agent_task_step'
  data: any
}

export function useAgentChat() {
  // 基础状态
  const task = ref('')
  const chatHistory = ref<(ChatMessage | AgentMessage)[]>([])
  const isLoading = ref(false)
  const isWaitingForHelp = ref(false)
  const currentTaskId = ref<string | null>(null)
  const assistanceResponse = ref('')
  
  // AbortController 用于取消任务
  let abortController: AbortController | null = null
  
  return {
    task,
    chatHistory,
    isLoading,
    isWaitingForHelp,
    currentTaskId,
    assistanceResponse,
    // ... 方法
  }
}
```

### 2. SSE 连接处理
```typescript
// utils/sseHandler.ts
export async function handleAgentTask(
  requestBody: AgentRunRequest,
  onMessage: (event: AgentMessage) => void,
  onError: (error: Error) => void,
  signal?: AbortSignal
) {
  try {
    const response = await fetch(`${BACKEND_API_URL}/api/agent/run`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody),
      signal
    })

    if (!response.body) throw new Error('Response body is null')

    const reader = response.body.getReader()
    const decoder = new TextDecoder()

    while (true) {
      const { done, value } = await reader.read()
      if (done) break

      const chunk = decoder.decode(value, { stream: true })
      const lines = chunk.split('\n\n').filter(line => line.trim())

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const jsonString = line.substring(6)
          if (jsonString.trim()) {
            try {
              const eventData = JSON.parse(jsonString)
              onMessage(eventData)
            } catch (e) {
              console.error('Failed to parse SSE data:', jsonString, e)
            }
          }
        }
      }
    }
  } catch (error) {
    if (error instanceof DOMException && error.name === 'AbortError') {
      // 任务被用户取消
      return
    }
    onError(error as Error)
  }
}
```

### 3. 主要方法实现
```typescript
// composables/useAgentChat.ts (续)
export function useAgentChat() {
  // ... 状态定义

  // 启动新任务
  const handleNewTask = async () => {
    if (!task.value.trim() || isLoading.value) return

    isLoading.value = true
    chatHistory.value = []
    currentTaskId.value = null

    // 添加用户消息
    chatHistory.value.push({ role: 'user', content: task.value })

    // 创建 AbortController
    abortController = new AbortController()

    const requestBody = {
      task: task.value,
      settings: {
        llm_provider: "deepseek",
        llm_model_name: "deepseek-chat",
        llm_temperature: 0.6,
        use_vision: true,
        max_steps: 100,
        max_actions_per_step: 10,
        max_input_tokens: 128000,
        headless: false,
        save_agent_history_path: "./tmp/agent_history",
        save_recording_path: "./tmp/recordings",
        save_trace_path: "./tmp/traces",
        save_download_path: "./tmp/downloads",
        window_w: 2560,
        window_h: 1600,
        keep_browser_open: false,
        disable_security: false
      }
    }

    await handleAgentTask(
      requestBody,
      handleSSEMessage,
      handleSSEError,
      abortController.signal
    )

    task.value = ''
    isLoading.value = false
  }

  // 处理 SSE 消息
  const handleSSEMessage = (eventData: AgentMessage) => {
    console.log('📨 SSE消息:', eventData.type, eventData)

    switch (eventData.type) {
      case 'step':
        chatHistory.value.push(eventData)
        break

      case 'assistance_request':
        isWaitingForHelp.value = true
        currentTaskId.value = eventData.data.request_id
        chatHistory.value.push(eventData)
        break

      case 'done':
        chatHistory.value.push(eventData)
        isLoading.value = false
        isWaitingForHelp.value = false
        currentTaskId.value = null
        break

      case 'error':
        chatHistory.value.push({
          role: 'assistant',
          content: `错误: ${eventData.data}`
        })
        isLoading.value = false
        isWaitingForHelp.value = false
        break

      case 'agent_task_step':
        // 记录任务步骤，可用于历史回放
        console.log('任务步骤:', eventData.data)
        break
    }
  }

  // 响应协助请求
  const handleAssistanceResponse = async () => {
    if (!assistanceResponse.value.trim() || !currentTaskId.value) return

    chatHistory.value.push({
      role: 'user',
      content: assistanceResponse.value
    })

    try {
      await fetch(`${BACKEND_API_URL}/api/agent/respond`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          task_id: currentTaskId.value,
          response: assistanceResponse.value
        })
      })
      assistanceResponse.value = ''
      isWaitingForHelp.value = false
    } catch (error) {
      console.error('Failed to submit assistance:', error)
    }
  }

  // 取消任务
  const handleAbortTask = () => {
    if (abortController) {
      abortController.abort()
      abortController = null
    }
    isLoading.value = false
    isWaitingForHelp.value = false
    currentTaskId.value = null
    chatHistory.value.push({
      role: 'assistant',
      content: '任务已被用户取消'
    })
  }

  return {
    // 状态
    task,
    chatHistory,
    isLoading,
    isWaitingForHelp,
    assistanceResponse,
    // 方法
    handleNewTask,
    handleAssistanceResponse,
    handleAbortTask
  }
}
```

## 🎨 Vue 组件模板实现

### 1. 主组件模板
```vue
<!-- components/AgentChat.vue -->
<template>
  <div class="agent-chat-container">
    <!-- 头部状态栏 -->
    <div class="header">
      <h2>AI Agent 智能助手</h2>
      <div class="status-indicator">
        <div
          :class="['status-dot', { 'loading': isLoading }]"
        ></div>
        <span class="status-text">
          {{ isLoading ? '正在执行' : '就绪' }}
        </span>
      </div>
    </div>

    <!-- 聊天区域 -->
    <div class="chat-container" ref="chatContainer">
      <div
        v-for="(message, index) in chatHistory"
        :key="index"
        class="message-item"
      >
        <!-- 用户消息 -->
        <div v-if="message.role === 'user'" class="user-message">
          <div class="message-content">{{ message.content }}</div>
        </div>

        <!-- AI 回复消息 -->
        <div v-else-if="message.role === 'assistant'" class="assistant-message">
          <div class="message-content">{{ message.content }}</div>
        </div>

        <!-- Agent 步骤消息 -->
        <div v-else-if="message.type === 'step'" class="step-message">
          <div class="step-header">
            <span class="step-number">步骤 {{ message.data.step }}</span>
          </div>

          <!-- 截图显示 -->
          <div v-if="message.data.screenshot" class="screenshot-container">
            <img
              :src="`data:image/png;base64,${message.data.screenshot}`"
              alt="Agent截图"
              class="screenshot"
              @click="showFullscreenImage(message.data.screenshot)"
            />
          </div>

          <!-- 步骤输出 -->
          <div class="step-output" v-html="message.data.output_html"></div>
        </div>

        <!-- 协助请求消息 -->
        <div v-else-if="message.type === 'assistance_request'" class="assistance-request">
          <div class="assistance-header">
            <Icon name="help-circle" />
            <span>Agent需要您的帮助</span>
          </div>
          <p class="assistance-query">{{ message.data.query }}</p>

          <!-- 回复输入框 -->
          <div v-if="!message.data.response && isWaitingForHelp" class="assistance-input">
            <el-input
              v-model="assistanceResponse"
              placeholder="请输入您的回复..."
              type="textarea"
              :rows="2"
            />
            <div class="assistance-actions">
              <el-button @click="handleAssistanceResponse" type="primary">
                发送回复
              </el-button>
            </div>
          </div>
        </div>

        <!-- 完成消息 -->
        <div v-else-if="message.type === 'done'" class="done-message">
          <div class="done-header">
            <Icon :name="message.data.success ? 'check-circle' : 'x-circle'" />
            <span>{{ message.data.success ? '任务完成' : '任务失败' }}</span>
          </div>
          <p class="done-summary">{{ message.data.summary }}</p>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
      <el-form @submit.prevent="handleSubmit">
        <div class="input-group">
          <el-input
            v-model="task"
            placeholder="请描述您希望AI Agent执行的任务..."
            type="textarea"
            :rows="3"
            :disabled="isLoading"
          />
          <div class="input-actions">
            <el-button
              v-if="isLoading"
              @click="handleAbortTask"
              type="danger"
            >
              取消任务
            </el-button>
            <el-button
              v-else
              @click="handleNewTask"
              type="primary"
              :disabled="!task.trim()"
            >
              执行任务
            </el-button>
          </div>
        </div>
      </el-form>
    </div>

    <!-- 全屏图片查看器 -->
    <el-dialog v-model="showImageDialog" title="截图查看" width="80%">
      <img :src="fullscreenImageSrc" alt="全屏截图" style="width: 100%;" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch } from 'vue'
import { useAgentChat } from '@/composables/useAgentChat'

// 使用组合式函数
const {
  task,
  chatHistory,
  isLoading,
  isWaitingForHelp,
  assistanceResponse,
  handleNewTask,
  handleAssistanceResponse,
  handleAbortTask
} = useAgentChat()

// 模板引用
const chatContainer = ref<HTMLElement>()

// 全屏图片查看
const showImageDialog = ref(false)
const fullscreenImageSrc = ref('')

// 显示全屏图片
const showFullscreenImage = (screenshot: string) => {
  fullscreenImageSrc.value = `data:image/png;base64,${screenshot}`
  showImageDialog.value = true
}

// 自动滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (chatContainer.value) {
      chatContainer.value.scrollTop = chatContainer.value.scrollHeight
    }
  })
}

// 监听聊天历史变化，自动滚动
watch(chatHistory, scrollToBottom, { deep: true })

// 处理表单提交
const handleSubmit = () => {
  if (isWaitingForHelp.value) {
    handleAssistanceResponse()
  } else {
    handleNewTask()
  }
}
</script>
```

### 2. 样式定义
```scss
<!-- components/AgentChat.vue (样式部分) -->
<style scoped lang="scss">
.agent-chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  h2 {
    margin: 0;
    color: #1f2937;
    font-size: 1.25rem;
    font-weight: 600;
  }
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 9999px;

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10b981;

    &.loading {
      background: #f59e0b;
      animation: pulse 2s infinite;
    }
  }

  .status-text {
    font-size: 0.875rem;
    font-weight: 500;
    color: #0369a1;
  }
}

.chat-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message-item {
  max-width: 100%;
}

.user-message {
  display: flex;
  justify-content: flex-end;

  .message-content {
    background: #3b82f6;
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 1rem 1rem 0.25rem 1rem;
    max-width: 70%;
    word-wrap: break-word;
  }
}

.assistant-message {
  display: flex;
  justify-content: flex-start;

  .message-content {
    background: white;
    color: #1f2937;
    padding: 0.75rem 1rem;
    border-radius: 1rem 1rem 1rem 0.25rem;
    max-width: 70%;
    word-wrap: break-word;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}

.step-message {
  background: white;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #3b82f6;

  .step-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;

    .step-number {
      background: #3b82f6;
      color: white;
      padding: 0.25rem 0.75rem;
      border-radius: 9999px;
      font-size: 0.875rem;
      font-weight: 500;
    }
  }

  .screenshot-container {
    margin: 0.75rem 0;

    .screenshot {
      max-width: 100%;
      height: auto;
      border-radius: 0.5rem;
      cursor: pointer;
      transition: transform 0.2s;

      &:hover {
        transform: scale(1.02);
      }
    }
  }

  .step-output {
    font-size: 0.875rem;
    color: #4b5563;
    line-height: 1.5;
  }
}

.assistance-request {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 0.75rem;
  padding: 1rem;

  .assistance-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    color: #92400e;
    font-weight: 600;
  }

  .assistance-query {
    color: #78350f;
    margin-bottom: 1rem;
    line-height: 1.5;
  }

  .assistance-input {
    .assistance-actions {
      margin-top: 0.75rem;
      display: flex;
      justify-content: flex-end;
    }
  }
}

.done-message {
  background: #d1fae5;
  border: 1px solid #10b981;
  border-radius: 0.75rem;
  padding: 1rem;

  .done-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    color: #065f46;
    font-weight: 600;
  }

  .done-summary {
    color: #047857;
    line-height: 1.5;
  }
}

.input-area {
  background: white;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;

  .input-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .input-actions {
    display: flex;
    justify-content: flex-end;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .user-message .message-content,
  .assistant-message .message-content {
    max-width: 90%;
  }

  .header {
    padding: 0.75rem 1rem;

    h2 {
      font-size: 1.125rem;
    }
  }

  .input-area {
    padding: 0.75rem 1rem;
  }
}
</style>
```

## 🔧 工具函数和类型定义

### 1. 类型定义文件
```typescript
// types/agent.ts
export interface ChatMessage {
  role: 'user' | 'assistant'
  content: string
}

export interface AgentStepData {
  step: number
  screenshot?: string
  output_html: string
}

export interface AgentAssistanceData {
  query: string
  request_id: string
  response?: string
}

export interface AgentDoneData {
  summary: string
  success?: boolean
}

export interface AgentTaskStep {
  step: number
  action: string
  content: string
  timestamp: number
}

export type AgentMessage = {
  type: 'step'
  data: AgentStepData
} | {
  type: 'assistance_request'
  data: AgentAssistanceData
} | {
  type: 'done'
  data: AgentDoneData
} | {
  type: 'error'
  data: string
} | {
  type: 'agent_task_step'
  data: AgentTaskStep
}

export interface AgentRunRequest {
  task: string
  settings: {
    llm_provider: string
    llm_model_name: string
    llm_temperature: number
    use_vision: boolean
    max_steps: number
    max_actions_per_step: number
    max_input_tokens: number
    headless: boolean
    save_agent_history_path: string
    save_recording_path: string
    save_trace_path: string
    save_download_path: string
    window_w: number
    window_h: number
    keep_browser_open: boolean
    disable_security: boolean
  }
}
```

### 2. API 工具函数
```typescript
// utils/api.ts
const BACKEND_API_URL = import.meta.env.VITE_BACKEND_API_URL || 'http://127.0.0.1:5008'

export async function apiPost(path: string, body: any) {
  const response = await fetch(`${BACKEND_API_URL}${path}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  })

  if (!response.ok) {
    throw new Error(await response.text())
  }

  return response.json()
}

export { BACKEND_API_URL }
```

## 📦 依赖安装

### 必需依赖
```bash
# Vue 3 + TypeScript 项目基础
npm install vue@^3.3.0 typescript@^5.0.0

# UI 框架 (选择其一)
npm install element-plus  # Element Plus
# 或
npm install ant-design-vue@^4.0.0  # Ant Design Vue
# 或
npm install naive-ui  # Naive UI

# HTTP 客户端
npm install axios@^1.5.0

# 图标库 (可选)
npm install @element-plus/icons-vue  # Element Plus 图标
```

### 开发依赖
```bash
npm install -D @types/node sass
```

## 🚀 部署配置

### 环境变量配置
```bash
# .env.development
VITE_BACKEND_API_URL=http://127.0.0.1:5008

# .env.production
VITE_BACKEND_API_URL=https://your-backend-domain.com
```

### Vite 配置
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:5008',
        changeOrigin: true,
      },
    },
  },
})
```

## ⚠️ 注意事项

1. **CORS 配置**: 确保后端正确配置 CORS 允许前端域名访问
2. **错误处理**: 实现完善的错误处理机制，包括网络错误、解析错误等
3. **内存管理**: 及时清理 AbortController 和事件监听器
4. **安全性**: 对用户输入进行适当的验证和清理
5. **性能优化**: 对大量聊天消息进行虚拟滚动优化
6. **移动端适配**: 确保在移动设备上的良好体验

## 🔍 调试建议

1. 在浏览器开发者工具中监控 Network 标签页的 SSE 连接
2. 使用 `console.log` 记录关键的 SSE 事件和状态变化
3. 实现详细的错误日志记录
4. 测试各种边界情况，如网络中断、任务取消等

## 📋 实现检查清单

### 核心功能
- [ ] SSE 连接和消息处理
- [ ] 任务启动和取消
- [ ] 用户协助请求处理
- [ ] 截图显示和全屏查看
- [ ] 聊天历史管理
- [ ] 错误处理和状态管理

### UI/UX
- [ ] 响应式设计
- [ ] 加载状态指示
- [ ] 消息类型区分显示
- [ ] 自动滚动到底部
- [ ] 输入验证和禁用状态

### 性能优化
- [ ] 组件懒加载
- [ ] 大量消息虚拟滚动
- [ ] 图片懒加载
- [ ] 内存泄漏防护

### 测试
- [ ] 单元测试
- [ ] 集成测试
- [ ] E2E 测试
- [ ] 错误场景测试

这份文档提供了完整的 AI Agent 功能移植指南，工程师可以根据这个文档在 Vue 项目中实现相同的功能。建议按照检查清单逐步实现和测试各个功能模块。
