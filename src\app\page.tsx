"use client";

import { useState } from "react";
import Navigation from "./components/Navigation";
import AgentChat from "./components/AgentChat";
import DbAgent from "./components/DbAgent";

export default function Home() {
  // Navigation state - 简化为只有AI Agent
  const [activeTab, setActiveTab] = useState<string>('agent-chat');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Left Navigation */}
      <Navigation activeTab={activeTab} setActiveTab={setActiveTab} />

      {/* Main Content */}
      <main className="ml-64 min-h-screen">
        <div className="h-screen flex flex-col">
          {/* Main Content Area - 移除了header，直接使用全高度 */}
          <div className="flex-1 p-6 overflow-hidden">
            <div className="h-full max-w-full mx-auto">
              {activeTab === 'agent-chat' && <AgentChat />}
              {activeTab === 'db-agent' && <DbAgent />}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
} 