# AI Agent 系统 API 接口文档

## 📋 概述

本文档详细描述了 AI Agent 系统的所有 API 接口，包括调用方式、参数、返回结果和调用流程。

**基础信息**
- 后端地址：`http://127.0.0.1:5008` 或 `http://10.177.88.16:5008`
- 请求格式：JSON
- 响应格式：JSON / Server-Sent Events (SSE)

## 🤖 AI Agent 相关接口

### 1. 启动 AI Agent 任务

**接口地址**：`POST /api/agent/run`

**请求参数**：
```json
{
  "task": "用户输入的任务描述",
  "settings": {
    "llm_provider": "deepseek",
    "llm_model_name": "deepseek-chat",
    "llm_temperature": 0.6,
    "use_vision": true,
    "max_steps": 100,
    "max_actions_per_step": 10,
    "max_input_tokens": 128000,
    "headless": false,
    "save_agent_history_path": "./tmp/agent_history",
    "save_recording_path": "./tmp/recordings",
    "save_trace_path": "./tmp/traces",
    "save_download_path": "./tmp/downloads",
    "window_w": 2560,
    "window_h": 1600,
    "keep_browser_open": false,
    "disable_security": false
  }
}
```

**参数说明**：
- `task`: 任务描述，支持自然语言
- `llm_provider`: LLM提供商 ("deepseek", "alibaba", "openai")
- `llm_model_name`: 模型名称
- `llm_temperature`: 温度参数 (0.0-1.0)
- `use_vision`: 是否启用视觉功能
- `max_steps`: 最大执行步骤数
- `headless`: 是否无头模式 (false启用截图)
- `window_w/window_h`: 浏览器窗口尺寸

**响应格式**：Server-Sent Events (SSE) 流式数据

**SSE 事件类型**：

#### 1.1 step 事件 - 执行步骤
```json
{
  "type": "step",
  "data": {
    "step": 1,
    "screenshot": "base64编码的截图数据",
    "output_html": "步骤输出的HTML内容"
  }
}
```

#### 1.2 assistance_request 事件 - 需要用户协助
```json
{
  "type": "assistance_request",
  "data": {
    "query": "Agent的询问内容",
    "request_id": "请求ID，用于回复",
    "response": "用户的回复（可选）"
  }
}
```

#### 1.3 done 事件 - 任务完成
```json
{
  "type": "done",
  "data": {
    "summary": "任务完成总结",
    "success": true
  }
}
```

#### 1.4 error 事件 - 错误信息
```json
{
  "type": "error",
  "data": "错误描述信息"
}
```

#### 1.5 agent_task_step 事件 - 任务步骤记录
```json
{
  "type": "agent_task_step",
  "data": {
    "step": 1,
    "action": "执行的动作",
    "content": "动作内容",
    "timestamp": 1640995200000
  }
}
```

### 2. 响应 Agent 协助请求

**接口地址**：`POST /api/agent/respond`

**请求参数**：
```json
{
  "task_id": "当前任务ID",
  "response": "用户的回复内容"
}
```

**响应结果**：
```json
{
  "status": "success"
}
```

**说明**：Agent会通过原有SSE连接继续发送更新事件

## 📁 文件上传接口

### 3. Excel 文件上传

**接口地址**：`POST /api/upload-excel`

**请求格式**：multipart/form-data

**请求参数**：
- `file`: Excel文件 (.xlsx, .xls)

**响应结果**：
```json
{
  "success": true,
  "file_path": "/uploads/excel/filename.xlsx",
  "message": "文件上传成功"
}
```

**错误响应**：
```json
{
  "success": false,
  "error": "文件格式不支持或上传失败"
}
```

## 🗄️ 数据库相关接口

### 4. 数据库连接测试

**接口地址**：`POST /api/db-agent/test-connection`

**请求参数**：
```json
{
  "type": "mysql",
  "host": "localhost",
  "port": 3306,
  "username": "root",
  "password": "password",
  "database": "test_db"
}
```

**响应结果**：
```json
{
  "success": true,
  "message": "连接成功"
}
```

### 5. 获取数据库连接状态

**接口地址**：`GET /api/db-agent/connection-status`

**响应结果**：
```json
{
  "connected": true,
  "database_info": {
    "type": "mysql",
    "host": "localhost",
    "database": "test_db"
  }
}
```

### 6. 获取数据库表结构

**接口地址**：`GET /api/db-agent/tables`

**响应结果**：
```json
{
  "success": true,
  "tables": [
    {
      "table_name": "users",
      "columns": [
        {
          "column_name": "id",
          "data_type": "int",
          "is_nullable": "NO",
          "column_key": "PRI"
        },
        {
          "column_name": "name",
          "data_type": "varchar",
          "is_nullable": "YES",
          "column_key": ""
        }
      ]
    }
  ]
}
```

### 7. 生成 SQL 查询

**接口地址**：`POST /api/db-agent/generate-sql`

**请求参数**：
```json
{
  "user_request": "查询所有用户的姓名和邮箱"
}
```

**响应结果**：
```json
{
  "success": true,
  "query_id": "uuid-string",
  "sql": "SELECT name, email FROM users",
  "explanation": "查询用户表中的姓名和邮箱字段"
}
```

### 8. 执行 SQL 查询

**接口地址**：`POST /api/db-agent/execute-sql`

**请求参数**：
```json
{
  "sql": "SELECT name, email FROM users LIMIT 10",
  "query_id": "uuid-string"
}
```

**响应结果**：
```json
{
  "success": true,
  "result": {
    "success": true,
    "data": [
      {"name": "张三", "email": "<EMAIL>"},
      {"name": "李四", "email": "<EMAIL>"}
    ],
    "columns": ["name", "email"],
    "row_count": 2,
    "execution_time": 15
  },
  "chart": {
    "chart_type": "table",
    "chart_image": "base64编码的图表图片"
  }
}
```

### 9. 生成数据图表

**接口地址**：`POST /api/db-agent/generate-chart`

**请求参数**：
```json
{
  "sql": "SELECT category, COUNT(*) as count FROM products GROUP BY category",
  "chart_type": "bar",
  "query_id": "uuid-string"
}
```

**响应结果**：
```json
{
  "success": true,
  "chart": {
    "chart_type": "bar",
    "chart_image": "base64编码的图表图片",
    "chart_config": {
      "title": "产品分类统计",
      "x_axis": "category",
      "y_axis": "count"
    }
  }
}
```

## 💾 数据库配置管理接口

### 10. 获取配置列表

**接口地址**：`GET /api/db/configs`

**响应结果**：
```json
{
  "success": true,
  "configs": ["生产环境", "测试环境", "开发环境"]
}
```

### 11. 获取指定配置

**接口地址**：`GET /api/db/configs/{config_name}`

**响应结果**：
```json
{
  "success": true,
  "config": {
    "type": "mysql",
    "host": "localhost",
    "port": 3306,
    "username": "root",
    "password": "password",
    "database": "test_db"
  }
}
```

### 12. 保存配置

**接口地址**：`POST /api/db/configs`

**请求参数**：
```json
{
  "name": "生产环境",
  "config": {
    "type": "mysql",
    "host": "prod.example.com",
    "port": 3306,
    "username": "prod_user",
    "password": "prod_password",
    "database": "prod_db"
  }
}
```

**响应结果**：
```json
{
  "success": true,
  "message": "配置保存成功"
}
```

### 13. 删除配置

**接口地址**：`POST /api/db/configs/{config_name}`

**请求参数**：
```json
{
  "method": "DELETE"
}
```

**响应结果**：
```json
{
  "success": true,
  "message": "配置删除成功"
}
```

## 📊 统计报表接口

### 14. 获取使用统计

**接口地址**：`GET /api/reports/usage-stats`

**响应结果**：
```json
{
  "success": true,
  "data": {
    "totalTasks": 150,
    "completedTasks": 120,
    "failedTasks": 30,
    "successRate": 80.0,
    "averageExecutionTime": 45.5,
    "mostUsedActions": [
      {"action": "click", "count": 45},
      {"action": "type", "count": 38}
    ],
    "dailyUsage": [
      {"date": "2024-01-01", "count": 12},
      {"date": "2024-01-02", "count": 15}
    ]
  }
}
```

### 15. 获取系统健康状态

**接口地址**：`GET /api/reports/system-health`

**响应结果**：
```json
{
  "success": true,
  "data": {
    "backend_status": "online",
    "browser_status": "ready",
    "llm_status": "connected",
    "last_check": "2024-01-01T12:00:00Z"
  }
}
```

## 🔄 完整调用流程

### AI Agent 任务执行流程

```
1. 用户输入任务
   ↓
2. [可选] 上传相关文件 (POST /api/upload-excel)
   ↓
3. 启动Agent任务 (POST /api/agent/run)
   ↓
4. 建立SSE连接，接收实时事件
   ↓
5. 处理不同类型的SSE事件：
   - step事件：显示执行步骤和截图
   - assistance_request事件：等待用户回复
   - done事件：任务完成
   - error事件：处理错误
   ↓
6. [如需协助] 发送用户回复 (POST /api/agent/respond)
   ↓
7. 继续接收SSE事件直到任务完成
```

### 数据库查询流程

```
1. 配置数据库连接
   ↓
2. 测试连接 (POST /api/db-agent/test-connection)
   ↓
3. 获取表结构 (GET /api/db-agent/tables)
   ↓
4. 用户输入查询需求
   ↓
5. 生成SQL (POST /api/db-agent/generate-sql)
   ↓
6. [可选] 用户编辑SQL
   ↓
7. 执行SQL (POST /api/db-agent/execute-sql)
   ↓
8. [可选] 生成图表 (POST /api/db-agent/generate-chart)
```

## ⚠️ 错误处理

### 通用错误格式

```json
{
  "success": false,
  "error": "错误描述信息",
  "error_code": "ERROR_CODE",
  "details": {
    "field": "具体错误字段",
    "message": "详细错误信息"
  }
}
```

### 常见错误码

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| `NETWORK_ERROR` | 网络连接失败 | 检查网络连接和服务器状态 |
| `INVALID_PARAMS` | 参数错误 | 检查请求参数格式和必填字段 |
| `DB_CONNECTION_FAILED` | 数据库连接失败 | 检查数据库配置和网络连接 |
| `SQL_EXECUTION_ERROR` | SQL执行错误 | 检查SQL语法和数据库权限 |
| `FILE_UPLOAD_ERROR` | 文件上传失败 | 检查文件格式和大小限制 |
| `AGENT_TIMEOUT` | Agent执行超时 | 简化任务或增加超时时间 |
| `LLM_API_ERROR` | LLM API调用失败 | 检查API密钥和配额 |

### HTTP 状态码

- `200`: 请求成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 禁止访问
- `404`: 资源不存在
- `500`: 服务器内部错误
- `503`: 服务不可用

## 🔧 调用示例

### JavaScript/TypeScript 调用示例

```typescript
// 基础API调用函数
const BACKEND_API_URL = 'http://127.0.0.1:5008'

async function apiPost(path: string, body: any) {
  const response = await fetch(`${BACKEND_API_URL}${path}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  })

  if (!response.ok) {
    throw new Error(await response.text())
  }

  return response.json()
}

// 启动Agent任务
async function startAgentTask(task: string) {
  const requestBody = {
    task,
    settings: {
      llm_provider: "deepseek",
      llm_model_name: "deepseek-chat",
      llm_temperature: 0.6,
      use_vision: true,
      max_steps: 100,
      headless: false,
      window_w: 1920,
      window_h: 1080
    }
  }

  const response = await fetch(`${BACKEND_API_URL}/api/agent/run`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(requestBody)
  })

  // 处理SSE流
  const reader = response.body.getReader()
  const decoder = new TextDecoder()

  while (true) {
    const { done, value } = await reader.read()
    if (done) break

    const chunk = decoder.decode(value, { stream: true })
    const lines = chunk.split('\n\n')

    for (const line of lines) {
      if (line.startsWith('data: ')) {
        const eventData = JSON.parse(line.substring(6))
        handleSSEEvent(eventData)
      }
    }
  }
}

// 处理SSE事件
function handleSSEEvent(eventData: any) {
  switch (eventData.type) {
    case 'step':
      console.log('执行步骤:', eventData.data.step)
      break
    case 'assistance_request':
      console.log('需要协助:', eventData.data.query)
      break
    case 'done':
      console.log('任务完成:', eventData.data.summary)
      break
    case 'error':
      console.error('执行错误:', eventData.data)
      break
  }
}

// 文件上传
async function uploadExcelFile(file: File) {
  const formData = new FormData()
  formData.append('file', file)

  const response = await fetch(`${BACKEND_API_URL}/api/upload-excel`, {
    method: 'POST',
    body: formData
  })

  return response.json()
}

// 数据库查询
async function generateSQL(userRequest: string) {
  return apiPost('/api/db-agent/generate-sql', {
    user_request: userRequest
  })
}
```

## 📝 注意事项

1. **SSE连接管理**：确保正确处理SSE连接的建立、维护和关闭
2. **文件上传限制**：Excel文件大小限制为50MB，支持.xlsx和.xls格式
3. **数据库连接**：确保数据库服务器允许外部连接
4. **API限流**：部分接口可能有调用频率限制
5. **安全性**：生产环境中应使用HTTPS协议
6. **超时设置**：长时间运行的任务建议设置合适的超时时间
7. **错误重试**：网络错误时建议实现指数退避重试机制
