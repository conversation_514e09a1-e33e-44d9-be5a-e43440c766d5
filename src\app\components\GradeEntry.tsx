'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { apiGet, apiPut, apiDelete } from '@/app/utils/api';
//import { apiGet, apiPost, apiPut, apiDelete, BACKEND_API_URL } from '@/app/utils/api';

interface Student {
  id: string;
  student_id: string;
  student_name: string;
  status: string;
  regular_score: number | null;
  homework_score: number | null;
  practical_score: number | null;
  exam_score: number | null;
  final_score: number | null;
  exam_status: string;
}

interface GradeFormData {
  student_id: string;
  student_name: string;
  status: string;
  regular_score: string;
  homework_score: string;
  practical_score: string;
  exam_score: string;
  final_score: string;
  exam_status: string;
}

export default function GradeEntry() {
  const [students, setStudents] = useState<Student[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm] = useState('');
  //const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [editingStudentId, setEditingStudentId] = useState<string | null>(null);
  //const [showAddForm, setShowAddForm] = useState(false);
  const [editFormData, setEditFormData] = useState<GradeFormData>({
    student_id: '',
    student_name: '',
    status: '正常',
    regular_score: '',
    homework_score: '',
    practical_score: '',
    exam_score: '',
    final_score: '',
    exam_status: '正常'
  });
  /*const [newStudentData, setNewStudentData] = useState<GradeFormData>({
    student_id: '',
    student_name: '',
    status: '正常',
    regular_score: '',
    homework_score: '',
    practical_score: '',
    exam_score: '',
    final_score: '',
    exam_status: '正常'
  });
  */

  const studentsPerPage = 10;

  // 加载学生列表
  const loadStudents = useCallback(async () => {
    setIsLoading(true);
    try {
      const data = await apiGet(`/api/students/scores?page=${currentPage}&per_page=${studentsPerPage}&search=${searchTerm}`);
      if (data.success) {
        setStudents(data.data);
      }
    } catch (error) {
      console.error('Failed to load students:', error);
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, searchTerm, studentsPerPage]);

  useEffect(() => {
    loadStudents();
  }, [currentPage, searchTerm, loadStudents]);

  // 处理行内编辑保存
  const handleInlineEdit = async (studentId: string) => {
    setIsLoading(true);
    try {
      const submitData = {
        ...editFormData,
        regular_score: editFormData.regular_score ? parseFloat(editFormData.regular_score) : null,
        homework_score: editFormData.homework_score ? parseFloat(editFormData.homework_score) : null,
        practical_score: editFormData.practical_score ? parseFloat(editFormData.practical_score) : null,
        exam_score: editFormData.exam_score ? parseFloat(editFormData.exam_score) : null,
        final_score: editFormData.final_score ? parseFloat(editFormData.final_score) : null,
      };

      const data = await apiPut(`/api/students/scores/${studentId}`, submitData);
      
      if (data.success) {
        loadStudents();
        setEditingStudentId(null);
        alert('成绩更新成功');
      } else {
        alert(data.error || '更新失败');
      }
    } catch (error) {
      console.error('Update failed:', error);
      alert('更新失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 处理新增学生
  /*
  const handleAddStudent = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      const submitData = {
        ...newStudentData,
        regular_score: newStudentData.regular_score ? parseFloat(newStudentData.regular_score) : null,
        homework_score: newStudentData.homework_score ? parseFloat(newStudentData.homework_score) : null,
        practical_score: newStudentData.practical_score ? parseFloat(newStudentData.practical_score) : null,
        exam_score: newStudentData.exam_score ? parseFloat(newStudentData.exam_score) : null,
        final_score: newStudentData.final_score ? parseFloat(newStudentData.final_score) : null,
      };

      const data = await apiPost(`${BACKEND_API_URL}/api/students/scores`, submitData, {
        headers: { 'Content-Type': 'application/json' },
      });
      
      if (data.success) {
        loadStudents();
        setShowAddForm(false);
        setNewStudentData({
          student_id: '',
          student_name: '',
          status: '正常',
          regular_score: '',
          homework_score: '',
          practical_score: '',
          exam_score: '',
          final_score: '',
          exam_status: '正常'
        });
        alert('成绩录入成功');
      } else {
        alert(data.error || '录入失败');
      }
    } catch (error) {
      console.error('Add failed:', error);
      alert('录入失败');
    } finally {
      setIsLoading(false);
    }
  };
  */

  // 开始编辑
  const startEdit = (student: Student) => {
    setEditingStudentId(student.id);
    setEditFormData({
      student_id: student.student_id,
      student_name: student.student_name,
      status: student.status,
      regular_score: student.regular_score?.toString() || '',
      homework_score: student.homework_score?.toString() || '',
      practical_score: student.practical_score?.toString() || '',
      exam_score: student.exam_score?.toString() || '',
      final_score: student.final_score?.toString() || '',
      exam_status: student.exam_status
    });
  };

  // 取消编辑
  const cancelEdit = () => {
    setEditingStudentId(null);
    setEditFormData({
      student_id: '',
      student_name: '',
      status: '正常',
      regular_score: '',
      homework_score: '',
      practical_score: '',
      exam_score: '',
      final_score: '',
      exam_status: '正常'
    });
  };

  // 删除学生
  const handleDelete = async (studentId: string) => {
    if (!confirm('确定要删除这条记录吗？')) return;

    try {
      const data = await apiDelete(`/api/students/scores/${studentId}`);
      if (data.success) {
        loadStudents();
        alert('删除成功');
      } else {
        alert(data.error || '删除失败');
      }
    } catch (error) {
      console.error('Delete failed:', error);
      alert('删除失败');
    }
  };

  return (
    <div className="h-full flex flex-col space-y-6 p-6">
      {/* 头部工具栏 */}
      {/*
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-2xl font-bold text-slate-900 dark:text-white">学生成绩管理</h2>
            <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
              录入和管理学生的各科成绩信息
            </p>
          </div>
          <button
            onClick={() => setShowAddForm(!showAddForm)}
            className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-emerald-500 to-green-600 text-white rounded-xl font-medium hover:from-emerald-600 hover:to-green-700 transition-all duration-200 hover:shadow-lg hover:shadow-emerald-500/25 active:scale-95"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            <span>{showAddForm ? '取消录入' : '录入成绩'}</span>
          </button>
        </div>

        <div className="relative">
          <input
            type="text"
            placeholder="搜索学生姓名或学号..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-4 py-3 pl-12 bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
          />
          <svg className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>
      */}
      
      {/* 新增学生表单 */}
      {/*
      {showAddForm && (
        <div className="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg p-6">
          <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-4">录入新学生成绩</h3>
          <form onSubmit={handleAddStudent} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">学号 *</label>
              <input
                type="text"
                required
                value={newStudentData.student_id}
                onChange={(e) => setNewStudentData({...newStudentData, student_id: e.target.value})}
                className="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">姓名 *</label>
              <input
                type="text"
                required
                value={newStudentData.student_name}
                onChange={(e) => setNewStudentData({...newStudentData, student_name: e.target.value})}
                className="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">平时成绩</label>
              <input
                type="number"
                min="0"
                max="100"
                step="0.1"
                value={newStudentData.regular_score}
                onChange={(e) => setNewStudentData({...newStudentData, regular_score: e.target.value})}
                className="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">作业成绩</label>
              <input
                type="number"
                min="0"
                max="100"
                step="0.1"
                value={newStudentData.homework_score}
                onChange={(e) => setNewStudentData({...newStudentData, homework_score: e.target.value})}
                className="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">实践成绩</label>
              <input
                type="number"
                min="0"
                max="100"
                step="0.1"
                value={newStudentData.practical_score}
                onChange={(e) => setNewStudentData({...newStudentData, practical_score: e.target.value})}
                className="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">考试成绩</label>
              <input
                type="number"
                min="0"
                max="100"
                step="0.1"
                value={newStudentData.exam_score}
                onChange={(e) => setNewStudentData({...newStudentData, exam_score: e.target.value})}
                className="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">总评成绩</label>
              <input
                type="number"
                min="0"
                max="100"
                step="0.1"
                value={newStudentData.final_score}
                onChange={(e) => setNewStudentData({...newStudentData, final_score: e.target.value})}
                className="w-full px-3 py-2 bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500"
              />
            </div>
            <div className="flex items-end">
              <button
                type="submit"
                disabled={isLoading}
                className="w-full px-4 py-2 bg-gradient-to-r from-emerald-500 to-green-600 text-white rounded-lg font-medium hover:from-emerald-600 hover:to-green-700 disabled:opacity-50 transition-all duration-200"
              >
                {isLoading ? '保存中...' : '保存'}
              </button>
            </div>
          </form>
        </div>
      )}
      */}

      {/* 学生列表 */}
      <div className="flex-1 bg-gray-50 border border-gray-200 rounded-lg overflow-hidden flex flex-col">
        <div className="flex-1 overflow-x-auto">
          <table className="w-full">
            <thead className="bg-slate-50 dark:bg-slate-700/50 border-b border-slate-200 dark:border-slate-600">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">学号</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">姓名</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">平时成绩</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">作业成绩</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">实践成绩</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">考试成绩</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">总评成绩</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">状态</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-slate-200 dark:divide-slate-600">
              {isLoading ? (
                <tr>
                  <td colSpan={9} className="px-6 py-12 text-center text-slate-500">
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-4 h-4 border-2 border-emerald-500 border-t-transparent rounded-full animate-spin"></div>
                      <span>加载中...</span>
                    </div>
                  </td>
                </tr>
              ) : students.length === 0 ? (
                <tr>
                  <td colSpan={9} className="px-6 py-12 text-center text-slate-500">
                    暂无学生记录
                  </td>
                </tr>
              ) : (
                students.map((student) => (
                  <tr key={student.id} className="hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-colors">
                    <td className="px-6 py-4 text-sm text-slate-900 dark:text-white">{student.student_id}</td>
                    <td className="px-6 py-4 text-sm font-medium text-slate-900 dark:text-white">{student.student_name}</td>
                    
                    {/* 平时成绩 */}
                    <td className="px-6 py-4 text-sm text-slate-600 dark:text-slate-300">
                      {editingStudentId === student.id ? (
                        <input
                          type="number"
                          min="0"
                          max="100"
                          step="0.1"
                          value={editFormData.regular_score}
                          onChange={(e) => setEditFormData({...editFormData, regular_score: e.target.value})}
                          className="w-20 px-2 py-1 text-sm bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500"
                        />
                      ) : (
                        student.regular_score ?? '-'
                      )}
                    </td>
                    
                    {/* 作业成绩 */}
                    <td className="px-6 py-4 text-sm text-slate-600 dark:text-slate-300">
                      {editingStudentId === student.id ? (
                        <input
                          type="number"
                          min="0"
                          max="100"
                          step="0.1"
                          value={editFormData.homework_score}
                          onChange={(e) => setEditFormData({...editFormData, homework_score: e.target.value})}
                          className="w-20 px-2 py-1 text-sm bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500"
                        />
                      ) : (
                        student.homework_score ?? '-'
                      )}
                    </td>
                    
                    {/* 实践成绩 */}
                    <td className="px-6 py-4 text-sm text-slate-600 dark:text-slate-300">
                      {editingStudentId === student.id ? (
                        <input
                          type="number"
                          min="0"
                          max="100"
                          step="0.1"
                          value={editFormData.practical_score}
                          onChange={(e) => setEditFormData({...editFormData, practical_score: e.target.value})}
                          className="w-20 px-2 py-1 text-sm bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500"
                        />
                      ) : (
                        student.practical_score ?? '-'
                      )}
                    </td>
                    
                    {/* 考试成绩 */}
                    <td className="px-6 py-4 text-sm text-slate-600 dark:text-slate-300">
                      {editingStudentId === student.id ? (
                        <input
                          type="number"
                          min="0"
                          max="100"
                          step="0.1"
                          value={editFormData.exam_score}
                          onChange={(e) => setEditFormData({...editFormData, exam_score: e.target.value})}
                          className="w-20 px-2 py-1 text-sm bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500"
                        />
                      ) : (
                        student.exam_score ?? '-'
                      )}
                    </td>
                    
                    {/* 总评成绩 */}
                    <td className="px-6 py-4 text-sm font-medium text-slate-900 dark:text-white">
                      {editingStudentId === student.id ? (
                        <input
                          type="number"
                          min="0"
                          max="100"
                          step="0.1"
                          value={editFormData.final_score}
                          onChange={(e) => setEditFormData({...editFormData, final_score: e.target.value})}
                          className="w-20 px-2 py-1 text-sm bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded focus:outline-none focus:ring-1 focus:ring-emerald-500"
                        />
                      ) : (
                        student.final_score ?? '-'
                      )}
                    </td>
                    
                    <td className="px-6 py-4">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                        student.status === '正常' 
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                      }`}>
                        {student.status}
                      </span>
                    </td>
                    
                    <td className="px-6 py-4 text-sm">
                      {editingStudentId === student.id ? (
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleInlineEdit(student.id)}
                            disabled={isLoading}
                            className="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 disabled:opacity-50"
                          >
                            保存
                          </button>
                          <button
                            onClick={cancelEdit}
                            className="text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300"
                          >
                            取消
                          </button>
                        </div>
                      ) : (
                        <div className="flex space-x-2">
                          <button
                            onClick={() => startEdit(student)}
                            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                          >
                            编辑
                          </button>
                          <button
                            onClick={() => handleDelete(student.id)}
                            className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                          >
                            删除
                          </button>
                        </div>
                      )}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* 分页控件 */}
        <div className="px-6 py-4 bg-slate-50 dark:bg-slate-700/50 border-t border-slate-200 dark:border-slate-600">
          <div className="flex items-center justify-between">
            <div className="text-sm text-slate-600 dark:text-slate-400">
              显示第 {currentPage} 页，每页 {studentsPerPage} 条记录
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 text-sm bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                上一页
              </button>
              <span className="px-3 py-1 text-sm text-slate-600 dark:text-slate-400">
                第 {currentPage} 页
              </span>
              <button
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={students.length < studentsPerPage}
                className="px-3 py-1 text-sm bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                下一页
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 