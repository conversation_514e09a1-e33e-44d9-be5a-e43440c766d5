"use client";

import { useState, useEffect } from "react";
import { apiGet, apiPost, BACKEND_API_URL } from '@/app/utils/api';

interface UsageStats {
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  successRate: number;
  averageExecutionTime: number;
  mostUsedActions: Array<{ action: string; count: number }>;
  dailyUsage: Array<{ date: string; count: number }>;
}

interface SystemHealth {
  backend_status: 'online' | 'offline';
  browser_status: 'ready' | 'busy' | 'error';
  llm_status: 'connected' | 'disconnected';
  last_check: string;
}

export default function Reports() {
  const [usageStats, setUsageStats] = useState<UsageStats | null>(null);
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadReports();
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(loadReports, 30000);
    
    return () => {
      clearInterval(interval);
    };
  }, []);

  const loadReports = async () => {
    setIsLoading(true);
    try {
      // Load usage statistics
      const statsData = await apiGet("/reports/usage-stats");
      if (statsData.success) {
        setUsageStats(statsData.stats);
      }

      // Load system health
      const healthData = await apiGet("/reports/system-health");
      if (healthData.success) {
        setSystemHealth(healthData.health);
      }
    } catch (error) {
      console.error("Failed to load reports:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const exportReport = async () => {
    try {
      const data = await apiPost("/reports/export", {
        method: "POST",
      });
      if (data.success) {
        // Download the report file
        window.open(`${BACKEND_API_URL}/${data.file_path}`, '_blank');
        alert("报表已生成并下载");
      } else {
        alert(data.error || "导出失败");
      }
    } catch (error) {
      console.error("Failed to export report:", error);
      alert("导出失败");
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
      case 'ready':
      case 'connected':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'busy':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      case 'offline':
      case 'error':
      case 'disconnected':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'online':
        return '在线';
      case 'offline':
        return '离线';
      case 'ready':
        return '就绪';
      case 'busy':
        return '忙碌';
      case 'error':
        return '错误';
      case 'connected':
        return '已连接';
      case 'disconnected':
        return '未连接';
      default:
        return status;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">AI Agent 使用报表</h2>
          <div className="flex gap-2">
            <button
              onClick={loadReports}
              disabled={isLoading}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? '刷新中...' : '刷新数据'}
            </button>
            <button
              onClick={exportReport}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
            >
              导出报表
            </button>
          </div>
        </div>
        <p className="text-gray-600 dark:text-gray-400">
          查看AI Agent的使用统计、系统状态和性能分析
        </p>
      </div>

      {/* System Health */}
      {systemHealth && (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-4">系统状态</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="text-2xl mb-2">🖥️</div>
              <div className="font-medium">后端服务</div>
              <div className={`px-3 py-1 rounded text-sm mt-2 ${getStatusColor(systemHealth.backend_status)}`}>
                {getStatusText(systemHealth.backend_status)}
              </div>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="text-2xl mb-2">🌐</div>
              <div className="font-medium">浏览器</div>
              <div className={`px-3 py-1 rounded text-sm mt-2 ${getStatusColor(systemHealth.browser_status)}`}>
                {getStatusText(systemHealth.browser_status)}
              </div>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="text-2xl mb-2">🤖</div>
              <div className="font-medium">AI模型</div>
              <div className={`px-3 py-1 rounded text-sm mt-2 ${getStatusColor(systemHealth.llm_status)}`}>
                {getStatusText(systemHealth.llm_status)}
              </div>
            </div>
          </div>
          <div className="mt-4 text-sm text-gray-500">
            最后检查时间: {systemHealth.last_check ? new Date(systemHealth.last_check).toLocaleString('zh-CN') : '未知'}
          </div>
        </div>
      )}

      {/* Usage Statistics */}
      {usageStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow text-center">
            <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">
              {usageStats.totalTasks}
            </div>
            <div className="text-gray-600 dark:text-gray-400 mt-1">总任务数</div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow text-center">
            <div className="text-3xl font-bold text-green-600 dark:text-green-400">
              {usageStats.completedTasks}
            </div>
            <div className="text-gray-600 dark:text-gray-400 mt-1">已完成</div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow text-center">
            <div className="text-3xl font-bold text-red-600 dark:text-red-400">
              {usageStats.failedTasks}
            </div>
            <div className="text-gray-600 dark:text-gray-400 mt-1">失败任务</div>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow text-center">
            <div className="text-3xl font-bold text-purple-600 dark:text-purple-400">
              {usageStats.successRate.toFixed(1)}%
            </div>
            <div className="text-gray-600 dark:text-gray-400 mt-1">成功率</div>
          </div>
        </div>
      )}

      {/* Performance Metrics */}
      {usageStats && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Most Used Actions */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4">常用操作统计</h3>
            {usageStats.mostUsedActions && usageStats.mostUsedActions.length > 0 ? (
              <div className="space-y-3">
                {usageStats.mostUsedActions.slice(0, 5).map((action, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm">{action.action}</span>
                    <div className="flex items-center gap-2">
                      <div className="w-24 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                        <div
                          className="bg-blue-500 h-2 rounded-full"
                          style={{
                            width: `${(action.count / usageStats.mostUsedActions[0].count) * 100}%`
                          }}
                        />
                      </div>
                      <span className="text-sm font-medium w-8 text-right">{action.count}</span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">暂无数据</div>
            )}
          </div>

          {/* Daily Usage Chart */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4">每日使用量</h3>
            {usageStats.dailyUsage && usageStats.dailyUsage.length > 0 ? (
              <div className="space-y-2">
                {usageStats.dailyUsage.slice(-7).map((day, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm">{new Date(day.date).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}</span>
                    <div className="flex items-center gap-2">
                      <div className="w-32 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                        <div
                          className="bg-green-500 h-2 rounded-full"
                          style={{
                            width: `${Math.max((day.count / Math.max(...usageStats.dailyUsage.map(d => d.count))) * 100, 5)}%`
                          }}
                        />
                      </div>
                      <span className="text-sm font-medium w-8 text-right">{day.count}</span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">暂无数据</div>
            )}
          </div>
        </div>
      )}

      {/* Performance Info */}
      {usageStats && (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-4">性能指标</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {usageStats.averageExecutionTime.toFixed(1)}s
              </div>
              <div className="text-gray-600 dark:text-gray-400 mt-1">平均执行时间</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {usageStats.totalTasks > 0 ? (usageStats.completedTasks / usageStats.totalTasks * 100).toFixed(1) : 0}%
              </div>
              <div className="text-gray-600 dark:text-gray-400 mt-1">任务完成率</div>
            </div>
          </div>
        </div>
      )}

      {/* Feature Descriptions */}
      <div className="bg-blue-50 dark:bg-blue-900 p-6 rounded-lg border-l-4 border-blue-400">
        <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">功能说明</h4>
        <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
          <li>• <strong>系统状态监控：</strong>实时监控后端服务、浏览器和AI模型的状态</li>
          <li>• <strong>使用统计：</strong>统计任务执行数量、成功率等关键指标</li>
          <li>• <strong>性能分析：</strong>分析平均执行时间和系统性能趋势</li>
          <li>• <strong>操作分布：</strong>分析最常用的浏览器操作类型</li>
          <li>• <strong>报表导出：</strong>支持将统计数据导出为Excel或PDF格式</li>
        </ul>
      </div>
    </div>
  );
} 