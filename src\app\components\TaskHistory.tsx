"use client";

import { useState, useEffect, useCallback } from "react";
import Image from "next/image";
import { apiGet, apiPost, BACKEND_API_URL } from '@/app/utils/api';

interface HistoryItem {
  id: string;
  task: string;
  timestamp: string;
  status: 'completed' | 'failed' | 'running';
  gif_path?: string;
  json_path?: string;
  summary?: string;
}

export default function TaskHistory() {
  const [historyItems, setHistoryItems] = useState<HistoryItem[]>([]);
  const [selectedItem, setSelectedItem] = useState<HistoryItem | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const loadHistory = useCallback(async () => {
    setIsLoading(true);
    try {
      const data = await apiGet(`history?search=${searchTerm}`);
      if (data.success) {
        setHistoryItems(data.history || []);
      }
    } catch (error) {
      console.error("Failed to load history:", error);
    } finally {
      setIsLoading(false);
    }
  }, [searchTerm]);

  useEffect(() => {
    loadHistory();
  }, [loadHistory]);

  const clearHistory = async () => {
    if (!confirm("确定要清空所有历史记录吗？")) return;
    
    try {
      const data = await apiPost("history", {
        method: "DELETE",
      });
      if (data.success) {
        setHistoryItems([]);
        setSelectedItem(null);
        alert("历史记录已清空");
      } else {
        alert(data.error || "清空失败");
      }
    } catch (error) {
      console.error("Failed to clear history:", error);
      alert("清空失败");
    }
  };

  const deleteHistoryItem = async (id: string) => {
    if (!confirm("确定要删除这条历史记录吗？")) return;
    
    try {
      const data = await apiPost(`history/${id}`, {
        method: "DELETE",
      });
      if (data.success) {
        setHistoryItems(prev => prev.filter(item => item.id !== id));
        if (selectedItem?.id === id) {
          setSelectedItem(null);
        }
      } else {
        alert(data.error || "删除失败");
      }
    } catch (error) {
      console.error("Failed to delete history item:", error);
      alert("删除失败");
    }
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('zh-CN');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'failed':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      case 'running':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'failed':
        return '失败';
      case 'running':
        return '运行中';
      default:
        return '未知';
    }
  };

  const filteredHistory = historyItems.filter(item =>
    item.task.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.summary?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
      {/* History List */}
      <div className="lg:col-span-1 bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">历史记录</h2>
          <div className="flex gap-2">
            <button
              onClick={loadHistory}
              disabled={isLoading}
              className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? '加载中...' : '刷新'}
            </button>
            <button
              onClick={clearHistory}
              className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700"
            >
              清空
            </button>
          </div>
        </div>

        {/* Search Bar */}
        <div className="mb-4">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="搜索任务..."
            className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
          />
        </div>

        {/* History Items */}
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {filteredHistory.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {isLoading ? '加载中...' : '暂无历史记录'}
            </div>
          ) : (
            filteredHistory.map((item) => (
              <div
                key={item.id}
                onClick={() => setSelectedItem(item)}
                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedItem?.id === item.id
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'
                    : 'border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-medium text-sm line-clamp-2">{item.task}</h3>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteHistoryItem(item.id);
                    }}
                    className="text-red-500 hover:text-red-700 ml-2"
                  >
                    ×
                  </button>
                </div>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span className={`px-2 py-1 rounded ${getStatusColor(item.status)}`}>
                    {getStatusText(item.status)}
                  </span>
                  <span>{formatTime(item.timestamp)}</span>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* History Detail */}
      <div className="lg:col-span-2 bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        {selectedItem ? (
          <div>
            <div className="flex items-start justify-between mb-4">
              <div>
                <h2 className="text-xl font-semibold">{selectedItem.task}</h2>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  {formatTime(selectedItem.timestamp)}
                </p>
              </div>
              <span className={`px-3 py-1 rounded ${getStatusColor(selectedItem.status)}`}>
                {getStatusText(selectedItem.status)}
              </span>
            </div>

            {/* Summary */}
            {selectedItem.summary && (
              <div className="mb-6">
                <h3 className="font-medium mb-2">任务摘要</h3>
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <p className="text-sm whitespace-pre-wrap">{selectedItem.summary}</p>
                </div>
              </div>
            )}

            {/* Media Content */}
            <div className="grid md:grid-cols-2 gap-6">
              {/* GIF Preview */}
              {selectedItem.gif_path && (
                <div>
                  <h3 className="font-medium mb-2">任务录制</h3>
                  <div className="bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
                    <Image
                      src={`${BACKEND_API_URL}/${selectedItem.gif_path}`}
                      alt="Task Recording"
                      width={400}
                      height={300}
                      className="w-full h-auto"
                    />
                  </div>
                </div>
              )}

              {/* JSON Data */}
              {selectedItem.json_path && (
                <div>
                  <h3 className="font-medium mb-2">详细数据</h3>
                  <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
                    <a
                      href={`${BACKEND_API_URL}/${selectedItem.json_path}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-500 hover:underline font-mono text-sm"
                    >
                      查看完整JSON数据 →
                    </a>
                  </div>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex gap-4 mt-6">
              <button
                onClick={() => {
                  // Re-run the same task
                  window.dispatchEvent(new CustomEvent('rerunTask', { 
                    detail: { task: selectedItem.task } 
                  }));
                }}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
              >
                重新运行此任务
              </button>
              <button
                onClick={() => deleteHistoryItem(selectedItem.id)}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"
              >
                删除此记录
              </button>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
            <div className="text-center">
              <p className="text-lg mb-2">📋</p>
              <p>选择左侧的历史记录查看详情</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 