"use client";

import { useState, FormEvent, useRef, useEffect, useCallback } from "react";
import Image from "next/image";
import { ChatMessage, AgentMessage, AgentAction, DoneAction } from './types';
import GradeEntry from './GradeEntry';
import { apiGet, apiPost, BACKEND_API_URL } from '@/app/utils/api';

// TaskHistory相关接口
interface HistoryItem {
  id: string;
  task: string;
  timestamp: string;
  status: 'completed' | 'failed' | 'running';
  gif_path?: string;
  json_path?: string;
  summary?: string;
}

// Reports相关接口


// 新闻数据接口
interface NewsItem {
  title: string;
  url?: string;
  time?: string;
  source?: string;
}

interface VideoItem {
  title: string;
  url?: string;
  views?: string;
  duration?: string;
  source?: string;
}

// 新增报表数据接口
interface ReportData {
  courseInfo: {
    course_name: string;
    class_name: string;
    teacher_name: string;
    total_students: number;
  };
  basicStatistics: {
    final_score_stats: {
      average: number;
      max: number;
      min: number;
      median: number;
      std: number;
    };
    component_stats: {
      regular_scores: { average: number; max: number; min: number };
      homework_scores: { average: number; max: number; min: number };
      practical_scores: { average: number; max: number; min: number };
      exam_scores: { average: number; max: number; min: number };
    };
  };
  classAnalysis?: {
    [className: string]: {
      student_count: number;
      average_score: number;
      max_score: number;
      min_score: number;
      median_score: number;
      pass_count: number;
      fail_count: number;
      pass_rate: number;
    };
  };
  failAnalysis: {
    total_students: number;
    fail_count: number;
    fail_rate: number;
    fail_rate_percentage: string;
    failing_students: Array<{
      name: string;
      student_id: string;
      class_name: string;
      final_score: number;
      exam_score: number;
      regular_score: number;
      gap: number;
    }>;
    is_high_fail_rate: boolean;
    fail_threshold: number;
  } | null;
  distributionAnalysis: {
    distribution: Array<{
      range: string;
      min_score: number;
      max_score: number;
      count: number;
      percentage: number;
    }>;
    rates: {
      excellent_rate: number;
      good_rate: number;
      pass_rate: number;
    };
    total_students: number;
  };
  rankingTable: Array<{
    rank: number;
    student_name: string;
    student_id: string;
    class_name: string;
    final_score: number;
    status: string;
  }>;
  charts: {
    score_distribution?: string;
    component_comparison?: string;
    ranking_chart?: string;
  };
  warningInfo?: {
    needs_warning: boolean;
    warning_message?: string;
    warning_details?: Record<string, unknown>;
  };
  email_status?: {
    report_email_sent: boolean;
    warning_email_sent: boolean;
  };
}

// 类型守卫函数
const isDoneAction = (action: AgentAction): action is DoneAction => {
  return 'done' in action && typeof action.done === 'object' && action.done !== null;
};

export default function AgentChat() {
  // Tab state
  const [activeTab, setActiveTab] = useState<'chat' | 'history' | 'reports' | 'grades'>('chat');

  // Agent state
  const [task, setTask] = useState("");
  const [chatHistory, setChatHistory] = useState<(ChatMessage | AgentMessage)[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isWaitingForHelp, setIsWaitingForHelp] = useState(false);
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);
  const [assistanceResponse, setAssistanceResponse] = useState("");
  const [abortController, setAbortController] = useState<AbortController | null>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // TaskHistory state
  const [historyItems, setHistoryItems] = useState<HistoryItem[]>([]);
  const [selectedItem, setSelectedItem] = useState<HistoryItem | null>(null);
  const [isHistoryLoading, setIsHistoryLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  // Reports state
  const [isReportsLoading, setIsReportsLoading] = useState(false);
  const [reportsData, setReportsData] = useState<ReportData | null>(null);

  useEffect(() => {
    // Scroll to the bottom of the chat history when it updates
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [chatHistory]);

  // Load history when history tab is active
  useEffect(() => {
    if (activeTab === 'history') {
      loadHistory();
    }
  }, [activeTab]); // eslint-disable-line react-hooks/exhaustive-deps

  // Load reports when reports tab is active
  useEffect(() => {
    if (activeTab === 'reports') {
      loadReports();
    }
  }, [activeTab]);

  // TaskHistory functions
  const loadHistory = useCallback(async () => {
    setIsHistoryLoading(true);
    try {
      const data = await apiGet(`/api/history?search=${searchTerm}`);
      if (data.success) {
        setHistoryItems(data.history || []);
      }
    } catch (error) {
      console.error("Failed to load history:", error);
    } finally {
      setIsHistoryLoading(false);
    }
  }, [searchTerm]);

  const clearHistory = async () => {
    if (!confirm("确定要清空所有历史记录吗？")) return;
    try {
      await apiPost('/api/history', {}, { method: 'DELETE' });
      loadHistory();
    } catch {
      alert('清空历史失败');
    }
  };



  // Reports functions
  const loadReports = async () => {
    setIsReportsLoading(true);
    try {
      // 生成综合成绩报表（从数据库）
      const reportResponse = await fetch(`${BACKEND_API_URL}/api/reports/generate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        }
      });
      const reportData = await reportResponse.json();
      
      if (reportData.success && reportData.report_data) {
        const report = reportData.report_data;
        
        // 设置完整的报表数据
        setReportsData({
          courseInfo: report.course_info,
          basicStatistics: report.basic_statistics,
          classAnalysis: report.class_analysis, // 新增班级分析
          failAnalysis: report.fail_analysis,
          distributionAnalysis: report.distribution_analysis,
          rankingTable: report.ranking_table,
          charts: report.charts,
          warningInfo: report.warning_info
        });
        

              } else {
          // 没有数据或生成失败
          setReportsData(null);
        }
      } catch (error) {
        console.error("Failed to load reports:", error);
        setReportsData(null);
      } finally {
      setIsReportsLoading(false);
    }
  };

  const exportReport = async () => {
    try {
      const response = await fetch(`${BACKEND_API_URL}/api/reports/export`, {
        method: "POST",
      });
      const data = await response.json();
      if (data.success) {
        // Download the report file
        window.open(`${BACKEND_API_URL}/${data.file_path}`, '_blank');
        alert("报表已生成并下载");
      } else {
        alert(data.error || "导出失败");
      }
    } catch (error) {
      console.error("Failed to export report:", error);
      alert("导出失败");
    }
  };

  const sendEmailReport = async () => {
    try {
      setIsReportsLoading(true);
      const response = await fetch(`${BACKEND_API_URL}/api/reports/send-email`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      const result = await response.json();

      if (result.success) {
        alert(`邮件发送成功！\n\n报表邮件: ${result.report_email_sent ? '已发送至教学管理邮箱' : '发送失败'}\n${result.has_warning ? `预警邮件: ${result.warning_email_sent ? '已发送至教务处' : '发送失败'}` : '当前无需发送预警邮件'}`);
      } else {
        alert(`邮件发送失败: ${result.error || '未知错误'}\n\n请检查邮件配置是否正确`);
      }
    } catch (error) {
      console.error("发送邮件时出错:", error);
      alert("发送邮件时出错，请检查网络连接和邮件配置");
    } finally {
      setIsReportsLoading(false);
    }
  };

  // Agent functions
  const handleHelpResponse = async (e: FormEvent) => {
    e.preventDefault();
    if (!currentTaskId || !task.trim()) return;

    // Temporarily disable input while submitting response
    setIsWaitingForHelp(false); 
    
    // Add user's help to chat history
    setChatHistory(prev => [...prev, { role: "user", content: task }]);

    try {
      await fetch(`${BACKEND_API_URL}/api/agent/respond`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          task_id: currentTaskId,
          response: task,
        }),
      });
      // The agent will now resume and send more updates through the existing stream.
      setTask(""); // Clear input after submitting
    } catch (error) {
      console.error("Failed to submit assistance:", error);
      setChatHistory(prev => [...prev, {role: 'assistant', content: 'Error submitting help to backend.'}]);
    }
  };

  const handleAssistanceResponse = async (requestId: string) => {
    if (!assistanceResponse.trim()) return;

    // Add user's response to chat history
    setChatHistory(prev => [...prev, { role: "user", content: assistanceResponse }]);

    try {
      await fetch(`${BACKEND_API_URL}/api/agent/respond`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          task_id: requestId,
          response: assistanceResponse,
        }),
      });
      setAssistanceResponse(""); // Clear input after submitting
    } catch (error) {
      console.error("Failed to submit assistance:", error);
      setChatHistory(prev => [...prev, {role: 'assistant', content: 'Error submitting help to backend.'}]);
    }
  };

  // 终止任务函数
  const handleAbortTask = async () => {
    if (abortController) {
      abortController.abort();
      setAbortController(null);
      setIsLoading(false);
      setIsWaitingForHelp(false);
      setCurrentTaskId(null);
      
      setChatHistory(prev => [...prev, { 
        role: 'assistant', 
        content: '<div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 text-red-700 dark:text-red-300"><strong>⚠️ 任务已被用户终止</strong></div>' 
      }]);
    }
  };

  const handleNewTask = async () => {
    if (!task.trim() || isLoading) return;

    setIsLoading(true);
    setChatHistory([]);
    setCurrentTaskId(null);

    // 创建新的AbortController
    const controller = new AbortController();
    setAbortController(controller);

    try {
      const response = await fetch(`${BACKEND_API_URL}/api/agent/run`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          task, 
          settings: { 
            // LLM Settings - configured for DeepSeek
            llm_provider: "deepseek", 
            llm_model_name: "deepseek-chat", // DeepSeek's chat model
            llm_temperature: 0.6,
            use_vision: true,
            max_steps: 100,
            max_actions_per_step: 10,
            max_input_tokens: 128000,
            // Browser Settings - changed to non-headless to enable screenshots
            headless: false, // 改为false以启用浏览器截图功能
            save_agent_history_path: "./tmp/agent_history",
            save_recording_path: "./tmp/recordings",
            save_trace_path: "./tmp/traces",
            save_download_path: "./tmp/downloads",
            window_w: 1280,
            window_h: 1100,
            keep_browser_open: false,
            disable_security: false
          }
        }),
        signal: controller.signal // 添加abort信号
      });

      if (!response.body) throw new Error("Response body is null");

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      
      // 清空聊天历史，等待后端发送chat事件
      setChatHistory([]);
      setTask("");

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split("\n\n").filter(line => line.trim());
        
        for (const line of lines) {
          if (line.startsWith("data: ")) {
            const jsonString = line.substring(6);
            if(jsonString.trim()){
              try {
                const eventData = JSON.parse(jsonString);
                console.log('📨 SSE消息:', eventData.type, eventData);
                
                // 检查是否包含重要的Result信息（优先处理）
                const dataString = JSON.stringify(eventData.data || eventData);
                
                // 更全面的Result检查 - 包括多种可能的格式
                if ((eventData.type !== 'step') && // 关键修复：确保步骤消息不会被误判为结果
                    (dataString.includes('Result:') || dataString.includes('📄 Result') || 
                    dataString.includes('"type":"result"') || dataString.includes('分析结果如下') ||
                    (eventData.type === 'result') || (eventData.data && eventData.data.result))) {
                  
                  let resultContent = '';
                  
                  // 尝试多种方式提取Result内容
                  if (eventData.data && eventData.data.result) {
                    resultContent = eventData.data.result;
                  } else if (eventData.data && typeof eventData.data === 'string') {
                    // 处理直接包含在data字段中的Result消息
                    if (eventData.data.includes('📄 Result:')) {
                      resultContent = eventData.data.replace(/.*?📄 Result:\s*/, '');
                    } else if (eventData.data.includes('Result:')) {
                      resultContent = eventData.data.replace(/.*?Result:\s*/, '');
                    } else {
                      resultContent = eventData.data;
                    }
                  } else if (dataString.includes('📄 Result:')) {
                    // 从JSON字符串中提取📄 Result内容
                    const resultMatch = dataString.match(/📄 Result:\s*"([^"]*(?:\\"[^"]*)*)"/) || 
                                      dataString.match(/📄 Result:\s*([^"]+?)(?=",|"$|$)/);
                    if (resultMatch) {
                      resultContent = resultMatch[1] || resultMatch[0].replace(/.*?📄 Result:\s*/, '');
                    }
                  } else if (dataString.includes('Result:')) {
                    // 从JSON字符串中提取Result内容
                    const resultMatch = dataString.match(/Result:\s*"([^"]*(?:\\"[^"]*)*)"/) || 
                                      dataString.match(/Result:\s*([^"]+?)(?=",|"$|$)/);
                    if (resultMatch) {
                      resultContent = resultMatch[1] || resultMatch[0].replace(/.*?Result:\s*/, '');
                    }
                  } else {
                    // 如果包含"分析结果如下"，尝试提取完整内容
                    if (dataString.includes('分析结果如下')) {
                      const analysisMatch = dataString.match(/"([^"]*分析结果如下[^"]*)"/) ||
                                          dataString.match(/分析结果如下(.+?)(?=",|"$|$)/);
                      if (analysisMatch) {
                        resultContent = analysisMatch[1] || analysisMatch[0];
                      }
                    } else {
                      resultContent = dataString;
                    }
                  }
                  
                  // 处理转义字符和引号
                  resultContent = resultContent
                    .replace(/\\n/g, '\n')
                    .replace(/\\"/g, '"')
                    .replace(/^"/, '')
                    .replace(/"$/, '')
                                        .trim();
                  
                  if (resultContent) {
                      setChatHistory(prev => [...prev, { 
                        role: 'assistant' as const, 
                        content: `<div class="bg-green-50 border border-green-200 rounded-lg p-4">
                          <div class="font-semibold text-green-800 mb-2">📄 任务已完成，分析结果如下:</div>
                          <div class="text-gray-800 whitespace-pre-line">${resultContent}</div>
                        </div>` 
                      }]);
                    continue;
                  }
                }
                
                // 过滤掉不需要的消息类型，但保留包含重要信息的消息
                if ((eventData.type === 'info' || eventData.type === 'output') && 
                    !dataString.includes('📄 Result') && !dataString.includes('Result:') &&
                    !dataString.includes('Task completed') && !dataString.includes('Successfully')) {
                  continue; // 跳过普通消息，但保留Result和任务完成消息
                }
                
                // 如果是info类型消息且包含Result信息，直接处理
                if ((eventData.type === 'info' || eventData.type === 'output') && 
                    (dataString.includes('📄 Result') || dataString.includes('Result:'))) {
                  // 这是Result消息，直接提取并显示
                  let resultContent = '';
                  if (eventData.data && typeof eventData.data === 'string') {
                    if (eventData.data.includes('📄 Result:')) {
                      resultContent = eventData.data.replace(/.*?📄 Result:\s*/, '');
                    } else if (eventData.data.includes('Result:')) {
                      resultContent = eventData.data.replace(/.*?Result:\s*/, '');
                    } else {
                      resultContent = eventData.data;
                    }
                  }
                  
                  if (resultContent.trim()) {
                    setChatHistory(prev => [...prev, { 
                      role: 'assistant' as const, 
                      content: `<div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="font-semibold text-green-800 mb-2">📄 任务已完成，分析结果如下:</div>
                        <div class="text-gray-800 whitespace-pre-line">${resultContent}</div>
                      </div>` 
                    }]);
                  }
                  continue;
                }
                
                // 检查是否是真正的任务完成消息
                if (eventData.type === 'status' && eventData.data === 'finished') {
                  // 检查是否包含任务完成的标识
                  const messageString = JSON.stringify(eventData);
                  if (messageString.includes('Task completed') || messageString.includes('Successfully')) {
                    // 这是真正的任务完成消息，显示它
                    setChatHistory(prev => [...prev, { 
                      role: 'assistant' as const, 
                      content: `<div class="text-center text-green-600 font-semibold py-2">✅ 任务执行完成</div>` 
                    }]);
                    // 继续处理，不要过滤掉
                  } else {
                    // 这是普通的finished状态消息，可以过滤
                    continue; 
                  }
                }
                
                if (eventData.type === 'chat') {
                  setChatHistory(prev => [...prev, eventData.data]);
                } else if (eventData.type === 'step') {
                  // 检查step消息是否包含done action
                  try {
                    const stepData = eventData.data;
                    if (stepData && stepData.output_html) {
                      const jsonMatch = stepData.output_html.match(/\{[\s\S]*"current_state"[\s\S]*\}/);
                      if (jsonMatch) {
                        const parsedData = JSON.parse(jsonMatch[0]);
                        if (parsedData.action && Array.isArray(parsedData.action)) {
                          const doneAction = parsedData.action.find((action: { done?: { text?: string } }) => action.done);
                          if (doneAction && doneAction.done && doneAction.done.text) {
                            console.log("📄 在SSE阶段检测到done action，添加Result消息");
                            // 先添加step消息
                            setChatHistory(prev => [...prev, eventData]);
                            // 然后添加Result消息
                            setChatHistory(prev => [...prev, { 
                              role: 'assistant' as const, 
                              content: `<div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                <div class="font-semibold text-green-800 mb-2">📄 任务已完成，分析结果如下:</div>
                                <div class="text-gray-800 whitespace-pre-line">${doneAction.done.text}</div>
                              </div>` 
                            }]);
                            continue; // 跳过后面的逻辑
                          }
                        }
                      }
                    }
                  } catch (e) {
                    console.log("检测step中done action失败:", e);
                  }
                  
                  // 普通step消息处理
                  setChatHistory(prev => [...prev, eventData]);
                                } else if (eventData.type === 'done') {
                  // 处理任务完成消息
                  
                  // 检查done消息中是否包含result信息
                  if (eventData.data && (eventData.data.text || eventData.data.result)) {
                    const resultText = eventData.data.text || eventData.data.result;
                    if (resultText && (resultText.includes('分析结果如下') || resultText.includes('新闻') || resultText.length > 50)) {
                        setChatHistory(prev => [...prev, { 
                          role: 'assistant' as const, 
                          content: `<div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div class="font-semibold text-green-800 mb-2">📄 任务已完成，分析结果如下:</div>
                            <div class="text-gray-800 whitespace-pre-line">${resultText}</div>
                          </div>` 
                        }]);
                    } else {
                      setChatHistory(prev => [...prev, eventData]);
                    }
                  } else {
                    setChatHistory(prev => [...prev, eventData]);
                  }
                } else if (eventData.type === 'error') {
                   setChatHistory(prev => [...prev, { role: 'assistant', content: `<strong>错误:</strong> ${eventData.data}` }]);
                } else if (eventData.type === 'assist') {
                   setChatHistory(prev => [...prev, { role: 'assistant', content: `<strong>需要帮助:</strong> ${eventData.data.query}` }]);
                   setCurrentTaskId(eventData.data.task_id);
                   setIsWaitingForHelp(true);
                } else if (eventData.type === 'auto_assist') {
                   // 处理自动协助事件
                   const isProgressQuery = eventData.data.response.includes('【批量成绩录入任务进度信息】');
                   
                   if (isProgressQuery) {
                     // 进度查询的特殊显示
                     setChatHistory(prev => [
                       ...prev, 
                       { role: 'assistant', content: `<strong>📊 任务进度查询:</strong> ${eventData.data.query}` },
                       { role: 'user', content: `<pre>${eventData.data.response}</pre>` },
                       { role: 'assistant', content: `<strong>✅ 已提供任务进度信息</strong>` }
                     ]);
                   } else {
                     // 学生数据查询的正常显示
                     setChatHistory(prev => [
                       ...prev, 
                       { role: 'assistant', content: `<strong>系统请求:</strong> ${eventData.data.query}` },
                       { role: 'user', content: eventData.data.response },
                       { role: 'assistant', content: `<strong>✅ ${eventData.data.message}</strong>` }
                     ]);
                   }
                } else {
                  // 检查是否是包含current_state和action的Agent输出消息
                  if (eventData.current_state && eventData.action) {
                    
                    // 检查是否包含step信息，如果包含则应该作为step处理
                    if (eventData.step || eventData.current_state.includes('Step')) {
                      // 提取步骤号
                      let stepNumber = eventData.step;
                      if (!stepNumber && eventData.current_state) {
                        const stepMatch = eventData.current_state.match(/Step (\d+)/);
                        stepNumber = stepMatch ? parseInt(stepMatch[1]) : 1;
                      }
                      
                      const stepMessage: AgentMessage = {
                        type: 'step' as const,
                        data: {
                          step: stepNumber,
                          output_html: JSON.stringify(eventData, null, 2)
                        }
                      };
                      
                      setChatHistory(prev => [...prev, stepMessage]);
                    } else {
                      // 不是步骤消息，检查是否是完成消息（包含done action）
                      // 查找done action并提取结果文本
                      const doneAction = eventData.action.find((action: AgentAction): action is DoneAction => isDoneAction(action));
                      const isSuccess = doneAction?.done?.success ?? true;
                      
                      const agentStateMessage: AgentMessage = {
                        type: 'done' as const,
                        data: {
                          summary: JSON.stringify(eventData, null, 2),
                          success: isSuccess
                        }
                      };
                      
                      setChatHistory(prev => [...prev, agentStateMessage]);
                    }
                  } else {
                    // 其他未知格式的消息，仍然显示原始JSON
                    setChatHistory(prev => [...prev, { 
                      role: 'assistant' as const, 
                      content: `<pre class="whitespace-pre-wrap text-sm bg-slate-50 p-4 rounded border">${JSON.stringify(eventData, null, 2)}</pre>` 
                    }]);
                   }
                }
              } catch (e) {
                console.error("Failed to parse SSE data chunk:", jsonString, e);
                // 在解析失败时，尝试提取有用信息而不是完全忽略
                if (jsonString.includes('"type":"step"')) {
                  // 即使解析失败，也显示步骤信息
                  const stepMatch = jsonString.match(/"step":(\d+)/);
                  if (stepMatch) {
                    const stepContent = `<strong>步骤 ${stepMatch[1]}</strong><br/>正在执行中...(截图数据过大)`;
                    setChatHistory(prev => [...prev, { role: 'assistant', content: stepContent }]);
                  }
                } else if (jsonString.includes('"type":"done"') && jsonString.includes('extracted_content')) {
                  // 尝试从失败的done消息中提取结果内容
                  console.log('🔧 尝试从损坏的done消息中提取结果');
                  const contentMatch = jsonString.match(/extracted_content['"]*:\s*['"]*([^'"]*(?:\\.[^'"]*)*)/);
                  if (contentMatch) {
                    const resultContent = contentMatch[1]
                      .replace(/\\n/g, '\n')
                      .replace(/\\"/g, '"')
                      .replace(/\\u[\da-f]{4}/gi, (match) => {
                        return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16));
                      });
                    
                    if (resultContent.length > 50) {
                      setChatHistory(prev => [...prev, { 
                        role: 'assistant' as const, 
                        content: `<div class="bg-green-50 border border-green-200 rounded-lg p-4">
                          <div class="font-semibold text-green-800 mb-2">📄 任务已完成，分析结果如下:</div>
                          <div class="text-gray-800 whitespace-pre-line">${resultContent}</div>
                        </div>` 
                      }]);
                    }
                  }
                }
              }
            }
          }
        }
      }
    } catch (error) {
      console.error("Failed to run agent task:", error);
      if (error instanceof DOMException && error.name === 'AbortError') {
        // 任务被中止，不显示错误消息（已在handleAbortTask中处理）
        // Task was aborted by user
      } else {
      setChatHistory(prev => [...prev, { role: 'assistant', content: "Failed to connect to the backend."}]);
      }
    } finally {
      setIsLoading(false);
      setIsWaitingForHelp(false);
      setCurrentTaskId(null);
      setAbortController(null);
    }
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (isWaitingForHelp) {
      handleHelpResponse(e);
    } else {
      handleNewTask();
    }
  };

  // 渲染消息的函数，处理不同类型的消息
  const renderMessage = (msg: ChatMessage | AgentMessage, index: number) => {
    // 处理标准聊天消息
    if ('role' in msg) {
      return (
        <div key={index} className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'} mb-4`}>
          <div className={`max-w-xl px-4 py-3 rounded-lg shadow-sm ${
          msg.role === 'user' 
              ? 'bg-blue-600 text-white ml-12' 
              : 'bg-white text-gray-900 border border-gray-200 mr-12'
        }`}>
            <div className="text-sm leading-relaxed" dangerouslySetInnerHTML={{ __html: msg.content }} />
          </div>
        </div>
      );
    }
    
    // 处理Agent消息类型
    if (msg.type === 'step') {
      const { step, output_html } = msg.data;
      
      // 检查是否是最后一个步骤
      const isLastStep = index === chatHistory.length - 1 || !chatHistory.slice(index + 1).some(m => 'type' in m && m.type === 'step');
      
      return (
        <div key={`step-${step}-${index}`} className="relative mb-4">
          {/* 步骤容器 */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">{step}</span>
              </div>
              <h4 className="font-semibold text-gray-900 text-lg">步骤 {step}</h4>
            </div>
          
            {/* 显示步骤输出 */}
            <div className="text-sm">
              {(() => {
                // 检查output_html是否包含JSON格式的Agent状态信息
                if (output_html.includes('current_state') || output_html.includes('evaluation_previous_goal')) {
                  try {
                    // 尝试从HTML中提取JSON
                    const jsonMatch = output_html.match(/\{[\s\S]*"current_state"[\s\S]*\}/);
                    if (jsonMatch) {
                      const jsonStr = jsonMatch[0];
                      console.log("🔍 步骤消息中检测到Agent状态JSON:", jsonStr);
                      
                      // 移除了有问题的setTimeout逻辑，改为在SSE阶段处理
                      
                      // 使用我们的转换函数
                      const formattedResult = formatTaskResult(jsonStr);
                      
                      return (
                        <div className="whitespace-pre-line text-blue-700 dark:text-blue-300">
                          {formattedResult.split('\n').map((line, lineIndex) => {
                            if (line.startsWith('**') && line.endsWith('**')) {
                              // 处理粗体标题
                              return (
                                <div key={lineIndex} className="font-semibold mt-3 mb-2 first:mt-0">
                                  {line.replace(/\*\*/g, '')}
                                </div>
                              );
                            } else if (line.startsWith('•')) {
                              // 处理列表项
                              return (
                                <div key={lineIndex} className="ml-4 my-1">
                                  {line}
                                </div>
                              );
                            } else if (line.trim()) {
                              // 处理普通文本
                              return (
                                <div key={lineIndex} className="my-1">
                                  {line}
                                </div>
                              );
                            } else {
                              // 空行
                              return <div key={lineIndex} className="h-2"></div>;
                            }
                          })}
                        </div>
                      );
                    }
                  } catch (e) {
                    console.error("步骤消息JSON解析失败:", e);
                  }
                }
                
                // 检查是否包含新闻数据或其他JSON数据
                if (output_html.includes('headline_news') || output_html.includes('hot_news') || output_html.includes('video_news') || output_html.includes('"title":')) {
                  try {
                    // 尝试提取JSON数据 - 更宽松的匹配
                    const jsonMatch = output_html.match(/\{[\s\S]*\}/);
                    if (jsonMatch) {
                      const jsonStr = jsonMatch[0];
                      
                      const newsData = JSON.parse(jsonStr);
                      let result = "**📰 页面内容提取结果**\n\n";
                      
                      if (newsData.headline_news && newsData.headline_news.length > 0) {
                        result += "**🔥 头条新闻：**\n";
                        newsData.headline_news.slice(0, 8).forEach((news: NewsItem, newsIndex: number) => {
                          result += `${newsIndex + 1}. ${news.title}\n`;
                        });
                        result += "\n";
                      }
                      
                      if (newsData.hot_news && newsData.hot_news.length > 0) {
                        result += "**💥 热点速递：**\n";
                        newsData.hot_news.slice(0, 6).forEach((news: NewsItem, newsIndex: number) => {
                          const timeInfo = news.time ? ` (${news.time})` : '';
                          const sourceInfo = news.source ? ` - ${news.source}` : '';
                          result += `${newsIndex + 1}. ${news.title}${timeInfo}${sourceInfo}\n`;
                        });
                        result += "\n";
                      }
                      
                      if (newsData.video_news && newsData.video_news.length > 0) {
                        result += "**🎥 热门视频：**\n";
                        newsData.video_news.slice(0, 4).forEach((news: NewsItem, newsIndex: number) => {
                          result += `${newsIndex + 1}. ${news.title}\n`;
                        });
                        result += "\n";
                      }
                      
                      if (newsData.featured_videos && newsData.featured_videos.length > 0) {
                        result += "**📺 精选视频：**\n";
                        newsData.featured_videos.slice(0, 4).forEach((video: VideoItem, videoIndex: number) => {
                          const viewInfo = video.views ? ` (${video.views}观看)` : '';
                          const durationInfo = video.duration ? ` [${video.duration}]` : '';
                          result += `${videoIndex + 1}. ${video.title}${viewInfo}${durationInfo}\n`;
                        });
                        result += "\n";
                      }
                      
                      result += "**✅ 数据提取完成** - 已成功解析并整理页面内容";
                      
                      return (
                        <div className="whitespace-pre-line text-blue-700 dark:text-blue-300">
                          {result.split('\n').map((line, lineIndex) => {
                            if (line.startsWith('**') && line.endsWith('**')) {
                              // 处理粗体标题
                              return (
                                <div key={lineIndex} className="font-semibold mt-3 mb-2 first:mt-0">
                                  {line.replace(/\*\*/g, '')}
                                </div>
                              );
                            } else if (line.match(/^\d+\.\s/)) {
                              // 处理编号列表项
                              return (
                                <div key={lineIndex} className="ml-4 my-1">
                                  {line}
                                </div>
                              );
                            } else if (line.trim()) {
                              // 处理普通文本
                              return (
                                <div key={lineIndex} className="my-1">
                                  {line}
                                </div>
                              );
                            } else {
                              // 空行
                              return <div key={lineIndex} className="h-2"></div>;
                            }
                          })}
                        </div>
                      );
                    }
                  } catch (e) {
                    console.error("新闻数据JSON解析失败:", e);
                    // 如果解析失败，尝试简单提取标题
                    if (output_html.includes('"title":')) {
                      const titleMatches = output_html.match(/"title":\s*"([^"]+)"/g);
                      if (titleMatches && titleMatches.length > 0) {
                        let result = "**📰 提取到的内容标题：**\n\n";
                        titleMatches.slice(0, 10).forEach((match, titleIndex) => {
                          const title = match.replace(/"title":\s*"/, '').replace(/"$/, '');
                          result += `${titleIndex + 1}. ${title}\n`;
                        });
                        result += "\n**✅ 内容提取完成**";
                        
                        return (
                          <div className="whitespace-pre-line text-blue-700 dark:text-blue-300">
                            {result.split('\n').map((line, lineIndex) => {
                              if (line.startsWith('**') && line.endsWith('**')) {
                                return (
                                  <div key={lineIndex} className="font-semibold mt-3 mb-2 first:mt-0">
                                    {line.replace(/\*\*/g, '')}
                                  </div>
                                );
                              } else if (line.match(/^\d+\.\s/)) {
                                return (
                                  <div key={lineIndex} className="ml-4 my-1">
                                    {line}
                                  </div>
                                );
                              } else if (line.trim()) {
                                return (
                                  <div key={lineIndex} className="my-1">
                                    {line}
                                  </div>
                                );
                              } else {
                                return <div key={lineIndex} className="h-2"></div>;
                              }
                            })}
                          </div>
                        );
                      }
                    }
                  }
                }
                
                // 检查是否包含文本格式的Agent信息（Eval, Memory, Next goal等）
                if (output_html.includes('Eval:') || output_html.includes('Memory:') || output_html.includes('Next goal:')) {
                  
                  let result = `✅ **当前状态**\n\n`;
                  
                  // 提取Eval信息
                  const evalMatch = output_html.match(/🤷 Eval:\s*([^\n]+)/);
                  if (evalMatch) {
                    result += `**上一目标结果**：${evalMatch[1]}\n\n`;
                  }
                  
                  // 提取Memory信息
                  const memoryMatch = output_html.match(/🧠 Memory:\s*([^\n]+)/);
                  if (memoryMatch) {
                    result += `**系统记忆**：\n`;
                    const memories = memoryMatch[1].split(/\d+\.\s+/).filter(mem => mem.trim());
                    memories.forEach((memory: string) => {
                      if (memory.trim()) {
                        result += `✓ ${memory.trim()}\n`;
                      }
                    });
                    result += `\n`;
                  }
                  
                  // 提取Next goal信息
                  const goalMatch = output_html.match(/🎯 Next goal:\s*([^\n]+)/);
                  if (goalMatch) {
                    result += `**后续计划**：${goalMatch[1]}\n\n`;
                  }
                  
                  // 提取Action信息
                  const actionMatch = output_html.match(/🛠️\s*Action[^:]*:\s*(\{[^}]+\})/);
                  if (actionMatch) {
                    try {
                      const actionData = JSON.parse(actionMatch[1]);
                      if (actionData.go_to_url) {
                        result += `🌐 **正在执行的动作**\n正在导航到：${actionData.go_to_url.url}\n\n`;
                      } else if (actionData.extract_content) {
                        result += `📊 **正在执行的动作**\n正在提取页面信息\n\n`;
                      } else if (actionData.done) {
                        result += `✅ **正在执行的动作**\n任务即将完成\n\n`;
                      }
                    } catch {
                      // Action JSON解析失败
                    }
                  }
                  
                  result += `✅ **执行结论**\n系统正在按计划执行任务，当前步骤已启动。`;
                  
                  return (
                    <div className="whitespace-pre-line text-blue-700 dark:text-blue-300">
                      {result.split('\n').map((line, lineIndex) => {
                        if (line.startsWith('**') && line.endsWith('**')) {
                          // 处理粗体标题
                          return (
                            <div key={lineIndex} className="font-semibold mt-3 mb-2 first:mt-0">
                              {line.replace(/\*\*/g, '')}
                            </div>
                          );
                        } else if (line.startsWith('✓')) {
                          // 处理列表项
                          return (
                            <div key={lineIndex} className="ml-4 my-1">
                              {line}
                            </div>
                          );
                        } else if (line.trim()) {
                          // 处理普通文本
                          return (
                            <div key={lineIndex} className="my-1">
                              {line}
                            </div>
                          );
                        } else {
                          // 空行
                          return <div key={lineIndex} className="h-2"></div>;
                        }
                      })}
                    </div>
                  );
                }
                
                // 如果不是特殊格式，显示原始内容的简化版本
                return (
                  <div className="text-gray-600">
                    <p>正在执行步骤操作...</p>
                  </div>
                );
              })()}
            </div>
          </div>
          
          {/* 步骤间的箭头连接器 */}
          {!isLastStep && (
            <div className="flex justify-center my-6">
              <svg className="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 16l-6-6h12l-6 6z" />
              </svg>
            </div>
          )}
        </div>
      );
    }
    
    if (msg.type === 'assistance_request') {
      const { query, request_id, response } = msg.data;
      return (
        <div key={`assistance-${index}`} className="bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 border border-amber-200 dark:border-amber-800 rounded-2xl p-6 mb-6 shadow-lg shadow-amber-500/10">
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center">
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-amber-800 dark:text-amber-200 font-semibold">🤝 Agent需要您的帮助</p>
          </div>
          <p className="text-amber-700 dark:text-amber-300 mb-4 leading-relaxed">{query}</p>
          {!response && (
            <div className="flex gap-3">
              <input
                type="text"
                value={assistanceResponse}
                onChange={(e) => setAssistanceResponse(e.target.value)}
                placeholder="请输入您的回复..."
                className="flex-1 px-4 py-3 bg-white dark:bg-slate-800 border border-amber-200 dark:border-amber-700 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
              />
              <button
                onClick={() => handleAssistanceResponse(request_id)}
                disabled={!assistanceResponse.trim() || isLoading}
                className="px-6 py-3 bg-amber-500 text-white rounded-xl hover:bg-amber-600 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-all duration-200"
              >
                发送
              </button>
            </div>
          )}
        </div>
      );
    }
    
    if (msg.type === 'done') {
      const { summary, success } = msg.data;
      const isSuccess = success !== false; // 默认为成功，除非明确标记为失败
      
      // 检查这个done消息是否原本是一个步骤消息
      // 如果summary中包含step信息或current_state，则应该显示为步骤而不是最终结果
      const isStepMessage = summary.includes('"step":') || summary.includes('Step') || summary.includes('current_state');
      
      if (isStepMessage) {
        // 这个done消息原本是步骤消息，重新处理为步骤
        try {
          const data = JSON.parse(summary);
          // 提取步骤号
          let stepNumber = data.step;
          if (!stepNumber && data.current_state) {
            const stepMatch = data.current_state.match(/Step (\d+)/);
            stepNumber = stepMatch ? parseInt(stepMatch[1]) : 1;
          }
          
          // 将这个done消息重新处理为step消息
          return renderMessage({
            type: 'step',
            data: {
              step: stepNumber,
              output_html: summary
            }
          } as AgentMessage, index);
          
        } catch (e) {
          console.error('解析步骤消息失败:', e);
        }
      }
      
      // 注意：不要将done action的text内容显示为最终结果
      // done消息应该只显示为普通的任务完成消息，不显示具体的text内容
      // 最终的Result内容应该来自独立的📄 Result消息
      
      return (
        <div key={`done-${index}`} className={`rounded-2xl p-6 mb-6 shadow-lg ${
          isSuccess 
            ? 'bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800 shadow-green-500/10'
            : 'bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 border border-amber-200 dark:border-amber-800 shadow-amber-500/10'
        }`}>
          <div className="flex items-center space-x-3 mb-3">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
              isSuccess ? 'bg-green-500' : 'bg-amber-500'
            }`}>
              {isSuccess ? (
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              ) : (
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              )}
            </div>
            <h4 className={`font-semibold text-lg ${
              isSuccess 
                ? 'text-green-700 dark:text-green-300' 
                : 'text-amber-700 dark:text-amber-300'
            }`}>
              {isSuccess ? '✅ 任务完成' : '⚠️ 任务执行遇到问题'}
            </h4>
          </div>
          
          {/* 转换后的用户友好描述 */}
          <div className={`text-sm leading-relaxed ${
            isSuccess 
              ? 'text-green-600 dark:text-green-400' 
              : 'text-amber-700 dark:text-amber-300'
          }`}>
            <div className="whitespace-pre-line">
              {formatTaskResult(summary).split('\n').map((line, index) => {
                if (line.startsWith('**') && line.endsWith('**')) {
                  // 处理粗体标题
                  return (
                    <div key={index} className="font-semibold mt-3 mb-2 first:mt-0">
                      {line.replace(/\*\*/g, '')}
                    </div>
                  );
                } else if (line.startsWith('•')) {
                  // 处理列表项
                  return (
                    <div key={index} className="ml-4 my-1">
                      {line}
                    </div>
                  );
                } else if (line.trim()) {
                  // 处理普通文本
                  return (
                    <div key={index} className="my-1">
                      {line}
                    </div>
                  );
                } else {
                  // 空行
                  return <div key={index} className="h-2"></div>;
                }
              })}
            </div>
          </div>
        </div>
      );
    }
    
    if (msg.type === 'error') {
      const errorMessage = msg.data;
      return (
        <div key={`error-${index}`} className="bg-gradient-to-r from-red-50 to-rose-50 dark:from-red-900/20 dark:to-rose-900/20 border border-red-200 dark:border-red-800 rounded-2xl p-6 mb-6 shadow-lg shadow-red-500/10">
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h4 className="font-semibold text-red-700 dark:text-red-300 text-lg">执行出错</h4>
          </div>
          <p className="text-red-600 dark:text-red-400 leading-relaxed">{errorMessage}</p>
        </div>
      );
    }
    
    // 对于其他类型的消息，显示原始JSON
    return (
      <div key={`message-${index}`} className="p-4 bg-gray-50 rounded-lg mb-4">
        <pre className="whitespace-pre-wrap text-sm">{JSON.stringify(msg, null, 2)}</pre>
      </div>
    );
  };

  // Utility functions
  // 生成有意义的任务名称
  const generateTaskName = (task: string) => {
    if (!task || task.trim() === '') return '未命名任务';
    
    // 清理和截断任务描述
    const cleanTask = task.trim();
    
    // 如果任务描述太长，截取前面的部分
    if (cleanTask.length > 20) {
      return cleanTask.substring(0, 20) + '...';
    }
    
    return cleanTask;
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('zh-CN');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
      case 'online':
      case 'ready':
      case 'connected':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'failed':
      case 'offline':
      case 'error':
      case 'disconnected':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      case 'running':
      case 'busy':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'failed':
        return '失败';
      case 'running':
        return '运行中';
      case 'online':
        return '在线';
      case 'offline':
        return '离线';
      case 'ready':
        return '就绪';
      case 'busy':
        return '忙碌';
      case 'error':
        return '错误';
      case 'connected':
        return '已连接';
      case 'disconnected':
        return '未连接';
      default:
        return status;
    }
  };

  const filteredHistory = historyItems.filter(item =>
    item.task.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.summary?.toLowerCase().includes(searchTerm.toLowerCase())
  );



      // 将技术术语转换为用户友好的描述
    const formatTaskResult = (summary: string) => {
    try {
      const data = JSON.parse(summary);

      const isTaskSuccess = !(data.action && data.action.some((a: AgentAction) => a.done && a.done.success === false));
      let result = "";

      result += `${isTaskSuccess ? '✅' : '❌'} **当前状态**\n\n`;

      // 从current_state中提取信息
      const currentState = data.current_state || {};
      
      if (currentState.evaluation_previous_goal) {
        const goalText = currentState.evaluation_previous_goal.replace(/成功 - |失败 - /g, '');
        result += `**上一目标结果**：${currentState.evaluation_previous_goal.startsWith('成功') ? '成功' : '失败'} - ${goalText}\n\n`;
      }

      if (currentState.memory) {
        result += `**系统记忆**：\n`;
        const memories = currentState.memory.split(/\d+\.\s+/).filter(Boolean);
        memories.forEach((memory: string) => {
          if (memory.trim()) result += `✓ ${memory.trim()}\n`;
        });
        result += `\n`;
      }

      if (currentState.next_goal) {
        const nextGoalText = currentState.next_goal === "任务已完成，无需进一步操作。" ? "无进一步操作需要" : currentState.next_goal;
        result += `**后续计划**：${nextGoalText}\n\n`;
      }

      const action = data.action && data.action[0];
      if (action) {
        if (action.go_to_url) {
          result += `🌐 **正在执行的动作**\n正在导航到：${action.go_to_url.url}\n\n`;
        } else if (action.click) {
          result += `👆 **正在执行的动作**\n正在点击页面元素\n\n`;
        } else if (action.type) {
          result += `⌨️ **正在执行的动作**\n正在输入文本内容\n\n`;
        } else if (action.scroll) {
          result += `📜 **正在执行的动作**\n正在滚动页面\n\n`;
        } else if (action.wait) {
          result += `⏳ **正在执行的动作**\n正在等待页面加载\n\n`;
        } else if (action.extract_content) {
          result += `📊 **正在执行的动作**\n正在提取页面信息\n\n`;
        } else if (action.done) {
          // 如果是done action，只显示任务完成状态，不显示具体内容
          result += `✅ **正在执行的动作**\n任务即将完成\n\n`;
        }
      }

      result += `\n${isTaskSuccess ? '✅' : '❌'} **执行结论**\n`;
      const isDoneAction = action && action.done !== undefined;

      if (isDoneAction) {
        result += isTaskSuccess ? '任务执行完成，结果将在下方单独显示。' : '任务执行中止，未能完成目标。';
      } else {
        result += '系统正在按计划执行任务，当前步骤已启动。';
      }

              return result;

    } catch (e) {
      console.error("formatTaskResult JSON解析或格式化失败:", e, "summary:", summary);
      return "正在分析步骤信息...";
    }
  };





  // Render Chat Tab
  const renderChatTab = () => (
    <div className="h-full flex flex-col p-6">
      
        {/* Chat Messages Area */}
        <div 
          ref={chatContainerRef} 
          className="flex-grow overflow-y-auto px-4 py-6 bg-gray-50 rounded-lg border border-gray-200 mb-6 min-h-0 space-y-4"
          style={{ maxHeight: 'calc(100vh - 300px)' }}
        >
          {chatHistory.length === 0 && !isLoading ? (
            <div className="flex flex-col items-center justify-center h-full text-center space-y-4">
              <h1 className="text-[3rem] font-semibold text-gray-700 mb-2">
              欢迎使用TeneAI
              </h1>
            <div>
                
                <p className="text-gray-500 max-w-md">
                  输入您想要完成的任务，AI Agent将自动操作浏览器帮您完成
                </p>
              </div>
              <div className="flex flex-wrap gap-2 mt-4">
              <div className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">
                  示例：分析新闻
                </div>
                <div className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">
                  示例：搜索商品
                </div>
                <div className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-xs">
                  示例：填写表单
                </div>
                <div className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs">
                  示例：数据采集
                </div>
              </div>
            </div>
          ) : (
            chatHistory.map((msg, index) => renderMessage(msg, index))
          )}
        </div>

        {/* Input Form - 简洁UI设计 */}
        <form onSubmit={handleSubmit} className="flex-shrink-0 mt-4">
          <div className="flex items-center bg-white border border-gray-200 rounded-full px-4 py-3 shadow-sm focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 transition-all duration-200">
            <input
              type="text"
              value={task}
              onChange={(e) => setTask(e.target.value)}
              placeholder={isWaitingForHelp ? "请提供所需信息..." : "描述您想要完成的任务，如：请分析腾讯新闻的今天头条新闻"}
              className="flex-1 px-2 py-1 bg-transparent text-gray-900 placeholder-gray-500 focus:outline-none"
              disabled={isLoading && !isWaitingForHelp}
            />
            
            {/* 发送按钮 */}
            {isLoading && !isWaitingForHelp ? (
              <button
                type="button"
                onClick={handleAbortTask}
                className="ml-2 w-8 h-8 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-all duration-200"
                title="终止任务"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            ) : (
              <button
                type="submit"
                className={`ml-2 w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 ${
                  isLoading && !isWaitingForHelp
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-500 hover:bg-blue-600 text-white'
                }`}
                disabled={isLoading && !isWaitingForHelp}
                title={isWaitingForHelp ? "提交回复" : "发送"}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              </button>
            )}
          </div>
        </form>
    </div>
  );

  // Render History Tab
  const renderHistoryTab = () => (
    <div className="h-full flex flex-col lg:flex-row gap-8 p-6">
      {/* Modern History List */}
      <div className="lg:w-1/3 bg-gray-50 border border-gray-200 rounded-lg p-6 flex flex-col">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-xl font-bold text-slate-900 dark:text-white">任务历史</h2>
            <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
              {filteredHistory.length} 个任务记录
            </p>
            </div>
          <div className="flex gap-2">
            <button
              onClick={loadHistory}
              disabled={isHistoryLoading}
              className="p-2 bg-blue-500 hover:bg-blue-600 text-white rounded-xl transition-all duration-200 disabled:opacity-50 group"
              title="刷新"
            >
              <svg className={`w-4 h-4 ${isHistoryLoading ? 'animate-spin' : 'group-hover:rotate-180 transition-transform duration-300'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
            <button
              onClick={clearHistory}
              className="p-2 bg-red-500 hover:bg-red-600 text-white rounded-xl transition-all duration-200 group"
              title="清空历史"
            >
              <svg className="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Modern Search Bar */}
        <div className="relative mb-6">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="搜索任务名称..."
            className="w-full pl-10 pr-4 py-3 bg-slate-50 dark:bg-slate-900/50 border border-slate-200 dark:border-slate-600 rounded-xl text-slate-900 dark:text-white placeholder-slate-500 dark:placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
          />
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
            <svg className="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>

        {/* Modern History Items */}
        <div className="flex-1 space-y-3 overflow-y-auto pr-2">
          {filteredHistory.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full py-12 text-center">
              <div className="w-16 h-16 bg-slate-100 dark:bg-slate-700 rounded-2xl flex items-center justify-center mb-4">
                <svg className="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <p className="text-slate-500 dark:text-slate-400 font-medium">
                {isHistoryLoading ? '正在加载...' : '暂无任务记录'}
              </p>
              <p className="text-xs text-slate-400 dark:text-slate-500 mt-1">
                {isHistoryLoading ? '' : '完成的任务将在这里显示'}
              </p>
            </div>
          ) : (
            filteredHistory.map((item) => (
              <div
                key={item.id}
                onClick={() => setSelectedItem(item)}
                className={`group relative p-4 border rounded-2xl cursor-pointer transition-all duration-200 ${
                  selectedItem?.id === item.id
                    ? 'border-blue-500 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 shadow-lg shadow-blue-500/10'
                    : 'border-slate-200 dark:border-slate-600 hover:border-blue-300 dark:hover:border-blue-700 hover:bg-slate-50 dark:hover:bg-slate-700/50 hover:shadow-md'
                }`}
              >
                <div className="flex items-start justify-between mb-3">
                  <h3 className="font-medium text-sm text-slate-900 dark:text-white line-clamp-2 leading-relaxed pr-2">
                    {generateTaskName(item.task)}
                  </h3>
                </div>
                <div className="flex items-center justify-between">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                    {getStatusText(item.status)}
                  </span>
                  <span className="text-xs text-slate-500 dark:text-slate-400">
                    {formatTime(item.timestamp)}
                  </span>
                </div>
                {selectedItem?.id === item.id && (
                  <div className="absolute -right-1 top-1/2 transform -translate-y-1/2">
                    <div className="w-1 h-8 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full"></div>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>

      {/* Modern History Detail */}
      <div className="lg:flex-1 bg-gray-50 border border-gray-200 rounded-lg p-6">
        {selectedItem ? (
          <div className="h-full flex flex-col">
            <div className="flex items-start justify-between mb-6 pb-4 border-b border-slate-200 dark:border-slate-700">
              <div className="flex-1">
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-2 leading-tight">
                  {generateTaskName(selectedItem.task)}
                </h2>
                <div className="flex items-center space-x-4 text-sm text-slate-600 dark:text-slate-400">
                  <div className="flex items-center space-x-1">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>{formatTime(selectedItem.timestamp)}</span>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedItem.status)}`}>
                    {getStatusText(selectedItem.status)}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto space-y-6">
              {/* 只保留任务录制，去掉摘要和详细数据 */}
              {selectedItem.gif_path && (
                <div>
                  <h3 className="flex items-center space-x-2 font-semibold text-slate-900 dark:text-white mb-3">
                    <svg className="w-5 h-5 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    <span>任务录制</span>
                  </h3>
                  <div className="bg-slate-100 dark:bg-slate-700 rounded-xl overflow-hidden shadow-lg">
                    <Image
                      src={`${BACKEND_API_URL}/${selectedItem.gif_path}`}
                      alt="Task Recording"
                      width={400}
                      height={300}
                      className="w-full h-auto"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="w-20 h-20 bg-slate-100 dark:bg-slate-700 rounded-3xl flex items-center justify-center mb-6 mx-auto">
                <svg className="w-10 h-10 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-slate-700 dark:text-slate-300 mb-2">
                选择一个任务记录
              </h3>
              <p className="text-slate-500 dark:text-slate-400 max-w-md">
                从左侧列表中选择一个任务记录，查看录制内容
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  // Render Reports Tab
  const renderReportsTab = () => {
    if (isReportsLoading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-500">正在生成报表数据...</p>
          </div>
        </div>
      );
    }

    if (!reportsData) {
      return (
        <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          <div className="text-gray-500">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">暂无报表数据</h3>
            <p className="mt-1 text-sm text-gray-500">数据库中没有学生成绩数据</p>
            <button
              onClick={loadReports}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              重新生成报表
            </button>
          </div>
        </div>
      );
    }

    return (
      <div className="flex flex-col space-y-6 p-6 overflow-y-auto" style={{maxHeight: 'calc(100vh - 120px)'}}>
        {/* 课程信息与数据统计分析 */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          {/* 课程信息头部 */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">{reportsData.courseInfo.course_name}</h2>
              <p className="text-gray-600 mt-1">
                班级：{reportsData.courseInfo.class_name} | 任课教师：{reportsData.courseInfo.teacher_name}
              </p>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={exportReport}
                disabled={isReportsLoading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center space-x-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span>导出报表</span>
              </button>
              <button
                onClick={sendEmailReport}
                disabled={isReportsLoading}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center space-x-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 7.89a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <span>发送邮件</span>
              </button>
            </div>
          </div>
          
          {/* 预警信息 */}
          {reportsData.warningInfo?.needs_warning && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-start">
                <svg className="w-5 h-5 text-red-500 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <div className="flex-1">
                  <h4 className="text-red-800 font-medium">高挂科率预警</h4>
                  <p className="text-red-700 mt-1">{reportsData.warningInfo.warning_message}</p>
                  <p className="text-red-600 mt-2 text-sm">
                    系统已自动向教务处发送预警邮件，包含详细的挂科学生名单和课程分析报告。
                  </p>
                </div>
              </div>
            </div>
          )}

          <h3 className="text-lg font-semibold mb-6">数据统计与班级分析</h3>
          
          {/* 基础统计数据 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-blue-50 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-blue-600">
                {reportsData.courseInfo.total_students}
              </div>
              <div className="text-gray-600 mt-1 text-sm">总学生数</div>
            </div>
            <div className="bg-orange-50 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-orange-600">
                {reportsData.basicStatistics.final_score_stats.average}
              </div>
              <div className="text-gray-600 mt-1 text-sm">平均分</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-green-600">
                {reportsData.distributionAnalysis.rates.pass_rate}%
              </div>
              <div className="text-gray-600 mt-1 text-sm">及格率</div>
            </div>
            <div className="bg-red-50 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-red-600">
                {reportsData.failAnalysis?.fail_count || 0}
              </div>
              <div className="text-gray-600 mt-1 text-sm">挂科人数</div>
            </div>
          </div>

          {/* 班级详细分析 */}
          {reportsData.classAnalysis && Object.keys(reportsData.classAnalysis).length > 0 && (
            <div>
              <h4 className="text-md font-semibold mb-4 text-gray-800">班级详细分析</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(reportsData.classAnalysis).map(([className, classStats]) => (
                  <div key={className} className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h5 className="font-medium text-gray-900 mb-3">{className}</h5>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">学生人数:</span>
                        <span className="font-medium">{classStats.student_count}人</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">平均分:</span>
                        <span className="font-medium">{classStats.average_score}分</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">及格率:</span>
                        <span className={`font-medium ${classStats.pass_rate >= 60 ? 'text-green-600' : 'text-red-600'}`}>
                          {classStats.pass_rate}%
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 考试成绩统计和成绩分布直方图 */}
        <div className="grid grid-cols-2 gap-6">
          {/* 左侧：考试成绩统计 */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4 text-center">考试成绩统计</h3>
            <div className="overflow-x-auto">
              <table className="min-w-full border-collapse border border-gray-300">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="border border-gray-300 px-3 py-2 text-center text-xs font-medium text-gray-700">分数分布</th>
                    <th className="border border-gray-300 px-3 py-2 text-center text-xs font-medium text-gray-700">90-100</th>
                    <th className="border border-gray-300 px-3 py-2 text-center text-xs font-medium text-gray-700">80-89</th>
                    <th className="border border-gray-300 px-3 py-2 text-center text-xs font-medium text-gray-700">70-79</th>
                    <th className="border border-gray-300 px-3 py-2 text-center text-xs font-medium text-gray-700">60-69</th>
                    <th className="border border-gray-300 px-3 py-2 text-center text-xs font-medium text-gray-700">50-59</th>
                    <th className="border border-gray-300 px-3 py-2 text-center text-xs font-medium text-gray-700">0-49</th>
                    <th className="border border-gray-300 px-3 py-2 text-center text-xs font-medium text-gray-700">平均分</th>
                    <th className="border border-gray-300 px-3 py-2 text-center text-xs font-medium text-gray-700">及格率</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="bg-white">
                    <td className="border border-gray-300 px-3 py-2 text-center text-sm font-medium text-gray-700">人数</td>
                    {reportsData.distributionAnalysis.distribution.map((dist, index) => (
                      <td key={index} className="border border-gray-300 px-3 py-2 text-center text-sm text-gray-900">
                        {dist.count}
                      </td>
                    ))}
                    <td className="border border-gray-300 px-3 py-2 text-center text-sm font-medium text-blue-600">
                      {reportsData.basicStatistics.final_score_stats.average}
                    </td>
                    <td className="border border-gray-300 px-3 py-2 text-center text-sm font-medium text-green-600">
                      {reportsData.distributionAnalysis.rates.pass_rate}%
                    </td>
                  </tr>
                  <tr className="bg-gray-50">
                    <td className="border border-gray-300 px-3 py-2 text-center text-sm font-medium text-gray-700">百分比</td>
                    {reportsData.distributionAnalysis.distribution.map((dist, index) => (
                      <td key={index} className="border border-gray-300 px-3 py-2 text-center text-sm text-gray-600">
                        {dist.percentage}%
                      </td>
                    ))}
                    <td className="border border-gray-300 px-3 py-2 text-center text-sm text-gray-400">-</td>
                    <td className="border border-gray-300 px-3 py-2 text-center text-sm text-gray-400">-</td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            {/* 补充统计信息 */}
            <div className="mt-4 grid grid-cols-3 gap-3 text-center">
              <div className="bg-blue-50 p-3 rounded">
                <div className="text-lg font-bold text-blue-600">{reportsData.courseInfo.total_students}</div>
                <div className="text-xs text-blue-700">考试人数</div>
              </div>
              <div className="bg-green-50 p-3 rounded">
                <div className="text-lg font-bold text-green-600">{reportsData.basicStatistics.final_score_stats.max}</div>
                <div className="text-xs text-green-700">最高分</div>
              </div>
              <div className="bg-red-50 p-3 rounded">
                <div className="text-lg font-bold text-red-600">{reportsData.basicStatistics.final_score_stats.min}</div>
                <div className="text-xs text-red-700">最低分</div>
              </div>
            </div>
          </div>

          {/* 右侧：成绩分布直方图 */}
          {reportsData.charts && reportsData.charts.score_distribution && (
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-4">成绩分布直方图</h3>
              <Image 
                src={`data:image/png;base64,${reportsData.charts.score_distribution}`}
                alt="成绩分布图"
                width={600}
                height={400}
                className="w-full rounded-lg border border-gray-200"
              />
            </div>
          )}
        </div>

        {/* 挂科学生名单 */}
        {reportsData.failAnalysis && reportsData.failAnalysis.failing_students.length > 0 && (
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4 text-red-600">
              挂科学生名单 ({reportsData.failAnalysis.fail_count}人)
            </h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">学号</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">班级</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">总评成绩</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">考试成绩</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">与及格线差距</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {reportsData.failAnalysis.failing_students.map((student, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{student.student_id}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{student.name}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{student.class_name}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium">{student.final_score}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{student.exam_score}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">{student.gap}分</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
                  )}

        {/* 成绩排名表 */}
        {reportsData.rankingTable && reportsData.rankingTable.length > 0 && (
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">成绩排名表 (前20名)</h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">排名</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">学号</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">班级</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">总评成绩</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {reportsData.rankingTable.slice(0, 20).map((student, index) => (
                      <tr key={index} className={index < 3 ? 'bg-yellow-50' : ''}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          #{student.rank}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{student.student_id}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{student.student_name}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{student.class_name}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">{student.final_score}</td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            student.status === '及格' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {student.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* 功能说明 */}
        <div className="bg-blue-50 p-6 rounded-lg border-l-4 border-blue-400">
          <h4 className="font-medium text-blue-800 mb-2">智能报表分析系统功能</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
            <div>
              <h5 className="font-medium mb-2">数据分析功能</h5>
              <ul className="space-y-1">
                <li>• <strong>汇总统计：</strong>自动计算各班级平均分、最高分、最低分</li>
                <li>• <strong>成绩排名：</strong>生成完整的学生成绩排名表，支持班级筛选</li>
                <li>• <strong>挂科识别：</strong>识别成绩&lt;60分的学生，生成补考名单</li>
                <li>• <strong>分数段统计：</strong>按90-100、80-89等区间统计人数分布</li>
              </ul>
            </div>
            <div>
              <h5 className="font-medium mb-2">智能预警系统</h5>
              <ul className="space-y-1">
                <li>• <strong>预警机制：</strong>挂科率超过30%时自动触发预警</li>
                <li>• <strong>邮件发送：</strong>自动发送报表至教学管理邮箱</li>
                <li>• <strong>预警邮件：</strong>高挂科率时自动通知教务处</li>
                <li>• <strong>可视化图表：</strong>生成分数分布直方图</li>
              </ul>
            </div>
          </div>
          <div className="mt-4 p-3 bg-white rounded border-l-4 border-green-400">
            <p className="text-green-700 text-sm">
              <strong>实时数据源：</strong>所有分析基于数据库中的真实学生成绩数据，支持实时更新和动态分析。
            </p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col bg-white rounded-lg shadow-sm">
      {/* 合并的导航和内容容器 */}
      <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
        {/* Tab切换区域 */}
        <div className="flex space-x-1 mb-6">
          <button
            onClick={() => setActiveTab('chat')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
              activeTab === 'chat'
                ? 'bg-blue-50 text-blue-700 border-2 border-blue-200'
                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 border-2 border-transparent'
            }`}
          >
            聊天
          </button>
          <button
            onClick={() => setActiveTab('history')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
              activeTab === 'history'
                ? 'bg-blue-50 text-blue-700 border-2 border-blue-200'
                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 border-2 border-transparent'
            }`}
          >
            历史
          </button>
          <button
            onClick={() => setActiveTab('reports')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
              activeTab === 'reports'
                ? 'bg-blue-50 text-blue-700 border-2 border-blue-200'
                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 border-2 border-transparent'
            }`}
          >
            报表
          </button>
          <button
            onClick={() => setActiveTab('grades')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
              activeTab === 'grades'
                ? 'bg-blue-50 text-blue-700 border-2 border-blue-200'
                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 border-2 border-transparent'
            }`}
          >
            成绩
          </button>
        </div>
        
        {/* 状态指示器 */}
        <div className="flex items-center space-x-2 px-3 py-1 bg-green-50 border border-green-200 rounded-full">
          <div className={`w-2 h-2 rounded-full ${isLoading ? 'bg-yellow-500 animate-pulse' : 'bg-green-500'}`}></div>
          <span className="text-xs font-medium text-green-700">
            {isLoading ? '正在执行' : '就绪'}
          </span>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 flex flex-col">
        {activeTab === 'chat' && renderChatTab()}
        {activeTab === 'history' && renderHistoryTab()}
        {activeTab === 'reports' && renderReportsTab()}
        {activeTab === 'grades' && <GradeEntry />}
      </div>
    </div>
  );
} 