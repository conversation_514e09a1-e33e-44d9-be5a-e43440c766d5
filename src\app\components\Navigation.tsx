'use client';

import React from 'react';

interface NavigationProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const Navigation: React.FC<NavigationProps> = ({ activeTab, setActiveTab }) => {
  const handleAIAgentClick = () => {
    setActiveTab('agent-chat');
  };

  return (
    <div className="fixed left-0 top-0 h-full w-64 bg-white shadow-lg border-r border-gray-200 z-30">
      <div className="h-full flex flex-col p-6">
        {/* Logo区域 */}
        <div className="flex items-center mb-8">
          <div className="h-8 w-8 bg-blue-600 rounded mr-2 flex items-center justify-center">
            <span className="text-white text-sm font-bold">AI</span>
          </div>
          <h1 className="text-xl font-bold text-gray-900">TeneAI AI Agent</h1>
        </div>
        
        {/* 导航按钮区域 */}
        <div className="flex-1">
          <nav className="space-y-2">
            <button
              onClick={handleAIAgentClick}
              className={`
                w-full flex items-center px-4 py-3 rounded-lg transition-all duration-200
                ${activeTab === 'agent-chat'
                  ? 'bg-blue-50 text-blue-700 border-2 border-blue-200'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 border-2 border-transparent'
                }
              `}
            >
              <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <div className="text-left">
                <div className="font-medium">AI Agent</div>
              </div>
            </button>

            <button
              onClick={() => setActiveTab('db-agent')}
              className={`
                w-full flex items-center px-4 py-3 rounded-lg transition-all duration-200
                ${activeTab === 'db-agent'
                  ? 'bg-blue-50 text-blue-700 border-2 border-blue-200'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 border-2 border-transparent'
                }
              `}
            >
              <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
              </svg>
              <div className="text-left">
                <div className="font-medium">DB Agent</div>
              </div>
            </button>
          </nav>
        </div>
        
        {/* 底部信息 */}
        <div className="mt-auto pt-4 border-t border-gray-200">
          <div className="text-sm text-gray-500">
            <div>版本 1.0.0</div>
            <div>© 2025 TeneAI AI Agent</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Navigation; 