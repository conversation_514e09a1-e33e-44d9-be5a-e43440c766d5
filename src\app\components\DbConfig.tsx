"use client";

import { useState, useEffect } from "react";
import { apiGet, apiPost } from '@/app/utils/api';

interface DatabaseConfig {
  type: 'mysql' | 'postgresql';
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
}

interface TableInfo {
  table_name: string;
  columns: Array<{
    column_name: string;
    data_type: string;
    is_nullable: string;
    column_key: string;
  }>;
}

export default function DbConfig() {
  const [config, setConfig] = useState<DatabaseConfig>({
    type: 'mysql',
    host: 'localhost',
    port: 3306,
    username: '',
    password: '',
    database: ''
  });

  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionMessage, setConnectionMessage] = useState('');
  const [tables, setTables] = useState<TableInfo[]>([]);

  // 新增：配置管理相关状态
  const [savedConfigs, setSavedConfigs] = useState<string[]>([]);
  const [selectedConfigName, setSelectedConfigName] = useState<string>('');
  const [newConfigName, setNewConfigName] = useState<string>('');
  const [isCreatingNew, setIsCreatingNew] = useState(false);

  // 加载已保存的配置列表
  useEffect(() => {
    loadSavedConfigsList();
  }, []);

  const loadSavedConfigsList = async () => {
    try {
      const data = await apiGet('/api/db/configs');
      if (data.success) {
        setSavedConfigs(data.configs);
      }
    } catch (error) {
      console.error('加载配置列表失败:', error);
    }
  };

  const loadNamedConfig = async (configName: string) => {
    try {
      const data = await apiGet(`/api/db/configs/${encodeURIComponent(configName)}`);
      if (data.success) {
        setConfig(data.config);
        setSelectedConfigName(configName);
        // 检查连接状态
        await checkConnectionStatus();
      } else {
        alert(`加载配置失败: ${data.error}`);
      }
    } catch (error) {
      console.error('加载配置失败:', error);
    }
  };

  const saveNamedConfig = async () => {
    const configName = isCreatingNew ? newConfigName : selectedConfigName;
    if (!configName.trim()) {
      alert('请输入配置名称');
      return;
    }

    try {
      const data = await apiPost('/api/db/configs', {
        name: configName,
        config: config
      });
      if (data.success) {
        alert(`配置 "${configName}" 保存成功！`);
        setSelectedConfigName(configName);
        setIsCreatingNew(false);
        setNewConfigName('');
        await loadSavedConfigsList();
      } else {
        alert(`保存失败: ${data.error}`);
      }
    } catch (error) {
      alert(`保存失败: ${error}`);
    }
  };

  const deleteNamedConfig = async (configName: string) => {
    if (!confirm(`确定要删除配置 "${configName}" 吗？`)) {
      return;
    }

    try {
      const data = await apiPost(`/api/db/configs/${encodeURIComponent(configName)}`, {
        method: 'DELETE'
      });
      if (data.success) {
        alert(`配置 "${configName}" 删除成功！`);
        if (selectedConfigName === configName) {
          setSelectedConfigName('');
          // 重置为默认配置
          setConfig({
            type: 'mysql',
            host: 'localhost',
            port: 3306,
            username: '',
            password: '',
            database: ''
          });
        }
        await loadSavedConfigsList();
      } else {
        alert(`删除失败: ${data.error}`);
      }
    } catch (error) {
      alert(`删除失败: ${error}`);
    }
  };

  const checkConnectionStatus = async () => {
    try {
      const data = await apiGet('/api/db-agent/connection-status');
      setIsConnected(data.connected);
      if (data.connected) {
        setConnectionMessage('数据库连接正常');
        loadTables();
      }
    } catch (error) {
      console.error('检查连接状态失败:', error);
    }
  };

  const handleConfigChange = (field: keyof DatabaseConfig, value: string | number) => {
    if (field === 'type') {
      // 当数据库类型改变时，自动更新默认端口
      const dbType = value as 'mysql' | 'postgresql';
      const defaultPort = dbType === 'mysql' ? 3306 : 5432;
      setConfig(prev => ({
        ...prev,
        type: dbType,
        port: defaultPort
      }));
    } else if (field === 'host') {
      // 特殊处理host字段，清理可能的异常字符
      const cleanHost = String(value).replace(/[#@]/g, '').trim();
      setConfig(prev => ({
        ...prev,
        [field]: cleanHost
      }));
    } else {
      setConfig(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const testConnection = async () => {
    setIsConnecting(true);
    setConnectionMessage('');

    try {
      const payload = {
        type: config.type,
        host: config.host?.toString().trim() || '',
        port: config.port,
        username: config.username,
        password: config.password,
        database: config.database
      };

      const data = await apiPost('/api/db-agent/test-connection', payload);

      if (data.success) {
        setIsConnected(true);
        setConnectionMessage('连接成功！');
        loadTables();
      } else {
        setIsConnected(false);
        setConnectionMessage(`连接失败: ${data.error}`);
      }
    } catch (error) {
      setIsConnected(false);
      setConnectionMessage(`连接失败: ${error}`);
    } finally {
      setIsConnecting(false);
    }
  };

  const loadTables = async () => {
    try {
      const data = await apiGet('/api/db-agent/tables');

      if (data.success) {
        setTables(data.tables);
      }
    } catch (error) {
      console.error('加载表结构失败:', error);
    }
  };

  return (
    <div className="h-full flex flex-col p-6 overflow-y-auto">
      <div className="max-w-4xl mx-auto w-full">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">数据库配置</h2>

        {/* 配置管理区域 */}
        <div className="mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50">
          <h3 className="text-lg font-medium text-gray-900 mb-4">配置管理</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 选择已保存的配置 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">选择配置</label>
              <select
                value={selectedConfigName}
                onChange={(e) => {
                  const configName = e.target.value;
                  if (configName) {
                    loadNamedConfig(configName);
                  } else {
                    setSelectedConfigName('');
                  }
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">选择已保存的配置...</option>
                {savedConfigs.map(configName => (
                  <option key={configName} value={configName}>{configName}</option>
                ))}
              </select>
            </div>

            {/* 新建配置名称输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {isCreatingNew ? '新配置名称' : '当前配置'}
              </label>
              {isCreatingNew ? (
                <input
                  type="text"
                  value={newConfigName}
                  onChange={(e) => setNewConfigName(e.target.value)}
                  placeholder="输入新配置名称"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              ) : (
                <input
                  type="text"
                  value={selectedConfigName}
                  readOnly
                  placeholder="未选择配置"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100"
                />
              )}
            </div>
          </div>

          {/* 操作按钮 - 移到选择配置下面 */}
          <div className="mt-4 flex space-x-2">
            <button
              onClick={() => {
                setIsCreatingNew(!isCreatingNew);
                setNewConfigName('');
              }}
              className="px-3 py-1.5 text-sm bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              {isCreatingNew ? '取消' : '新建'}
            </button>
            
            <button
              onClick={saveNamedConfig}
              disabled={(!selectedConfigName && !isCreatingNew) || (isCreatingNew && !newConfigName.trim())}
              className="px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
            >
              保存
            </button>
            
            {selectedConfigName && (
              <button
                onClick={() => deleteNamedConfig(selectedConfigName)}
                className="px-3 py-1.5 text-sm bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                删除
              </button>
            )}
          </div>
        </div>

        {/* 连接状态指示器 */}
        <div className="mb-6 p-4 rounded-lg border">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className={`font-medium ${isConnected ? 'text-green-700' : 'text-red-700'}`}>
              {isConnected ? '已连接' : '未连接'}
            </span>
            {connectionMessage && (
              <span className="text-sm text-gray-600">- {connectionMessage}</span>
            )}
          </div>
        </div>

        {/* 数据库配置表单 */}
        <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold mb-4">连接配置</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 数据库类型 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                数据库类型
              </label>
              <select
                value={config.type}
                onChange={(e) => handleConfigChange('type', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="mysql">MySQL</option>
                <option value="postgresql">PostgreSQL</option>
              </select>
            </div>

            {/* 主机地址 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                主机地址
              </label>
              <input
                type="text"
                value={config.host}
                onChange={(e) => handleConfigChange('host', e.target.value)}
                placeholder="localhost"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* 端口 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                端口
              </label>
              <input
                type="number"
                value={config.port}
                onChange={(e) => handleConfigChange('port', parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* 用户名 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                用户名
              </label>
              <input
                type="text"
                value={config.username}
                onChange={(e) => handleConfigChange('username', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* 密码 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                密码
              </label>
              <input
                type="password"
                value={config.password}
                onChange={(e) => handleConfigChange('password', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* 数据库名 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                数据库名
              </label>
              <input
                type="text"
                value={config.database}
                onChange={(e) => handleConfigChange('database', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="mt-6 flex space-x-4">
            <button
              onClick={testConnection}
              disabled={isConnecting}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isConnecting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>测试连接</span>
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  <span>测试连接</span>
                </>
              )}
            </button>
          </div>
        </div>

        {/* 数据库概览 */}
        {isConnected && tables.length > 0 && (
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">数据库概览</h3>
            
            {/* 数据库统计 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{tables.length}</div>
                <div className="text-sm text-gray-500">表数量</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {tables.reduce((sum, table) => sum + table.columns.length, 0)}
                </div>
                <div className="text-sm text-gray-500">字段总数</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {tables.reduce((sum, table) => sum + table.columns.filter(col => col.column_key === 'PRI').length, 0)}
                </div>
                <div className="text-sm text-gray-500">主键数量</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{config.type.toUpperCase()}</div>
                <div className="text-sm text-gray-500">数据库类型</div>
              </div>
            </div>

            {/* 表列表 */}
            <div className="max-h-64 overflow-y-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">表名</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">字段数</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主键</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {tables.map((table) => {
                    const primaryKeys = table.columns.filter(col => col.column_key === 'PRI');
                    return (
                      <tr key={table.table_name} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {table.table_name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {table.columns.length}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {primaryKeys.length > 0 ? primaryKeys.map(pk => pk.column_name).join(', ') : '-'}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 