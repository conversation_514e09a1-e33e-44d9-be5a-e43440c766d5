"use client";

import { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { apiGet, apiPost, BACKEND_API_URL } from '@/app/utils/api';

interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
}

interface SqlQuery {
  id: string;
  original_request: string;
  generated_sql: string;
  explanation: string;
  is_editable: boolean;
  executed: boolean;
}

interface QueryResult {
  success: boolean;
  data: Record<string, unknown>[];
  columns: string[];
  row_count: number;
  execution_time: number;
  error?: string;
}

interface ChartData {
  chart_type: 'bar' | 'line' | 'pie' | 'table';
  chart_image?: string;
  chart_config?: Record<string, unknown>;
}

export default function DbAgentChat() {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentSql, setCurrentSql] = useState<SqlQuery | null>(null);
  const [queryResult, setQueryResult] = useState<QueryResult | null>(null);
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [sqlEditMode, setSqlEditMode] = useState(false);
  const [editedSql, setEditedSql] = useState('');
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // 检查数据库连接状态
  useEffect(() => {
    checkDatabaseConnection();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // 自动滚动到底部
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [messages]);

  const checkDatabaseConnection = async () => {
    try {
      const data = await apiGet('/api/db-agent/connection-status');
      setIsConnected(data.connected);
      if (!data.connected) {
        addSystemMessage('请先在"DB配置"页面连接数据库后再使用智能查询功能。');
      } else {
        addSystemMessage('数据库已连接，您可以开始提出查询需求了！');
      }
    } catch {
      setIsConnected(false);
      addSystemMessage('无法检查数据库连接状态，请确保后端服务正常运行。');
    }
  };

  const addSystemMessage = (content: string) => {
    setMessages(prev => [...prev, {
      role: 'system',
      content,
      timestamp: new Date()
    }]);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputMessage.trim() || isLoading || !isConnected) return;
    const userMessage: ChatMessage = {
      role: 'user',
      content: inputMessage,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);
    try {
      const data = await apiPost('/api/db-agent/generate-sql', { user_request: inputMessage });
      if (data.success) {
        const assistantMessage: ChatMessage = {
          role: 'assistant',
          content: data.explanation || '已为您生成SQL查询语句，请查看下方的SQL代码区域。',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, assistantMessage]);
        setCurrentSql({
          id: data.query_id,
          original_request: inputMessage,
          generated_sql: data.sql,
          explanation: data.explanation,
          is_editable: true,
          executed: false
        });
        setEditedSql(data.sql);
      } else {
        const errorMessage: ChatMessage = {
          role: 'assistant',
          content: `抱歉，无法理解您的请求：${data.error}`,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      const errorMessage: ChatMessage = {
        role: 'assistant',
        content: `处理请求时发生错误：${error}`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const executeSql = async () => {
    if (!currentSql || !editedSql.trim()) return;
    setIsLoading(true);
    try {
      const data = await apiPost('/api/db-agent/execute-sql', { sql: editedSql, query_id: currentSql.id });
      if (data.success) {
        setQueryResult(data.result);
        setCurrentSql(prev => prev ? {...prev, executed: true} : null);
        const successMessage: ChatMessage = {
          role: 'assistant',
          content: `✅ SQL执行成功！返回了 ${data.result.row_count} 行数据，执行时间：${data.result.execution_time}ms`,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, successMessage]);
        if (data.chart) {
          setChartData(data.chart);
        }
      } else {
        setQueryResult({
          success: false,
          data: [],
          columns: [],
          row_count: 0,
          execution_time: 0,
          error: data.error
        });
        const errorMessage: ChatMessage = {
          role: 'assistant',
          content: `SQL执行失败：${data.error}`,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      setQueryResult({
        success: false,
        data: [],
        columns: [],
        row_count: 0,
        execution_time: 0,
        error: String(error)
      });
      const errorMessage: ChatMessage = {
        role: 'assistant',
        content: `SQL执行异常：${error}`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const generateChart = async (chartType: string) => {
    if (!currentSql || !editedSql.trim()) {
      alert('请先执行SQL查询获取数据');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`${BACKEND_API_URL}/api/db-agent/generate-chart`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sql: editedSql,
          chart_type: chartType,
          query_id: currentSql.id
        })
      });

      const data = await response.json();

      if (data.success) {
        setChartData(data.chart);
        
        const successMessage: ChatMessage = {
          role: 'assistant',
          content: `📊 已生成${chartType === 'bar' ? '柱状图' : chartType === 'line' ? '折线图' : '饼图'}`,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, successMessage]);
      } else {
        alert(`生成图表失败：${data.error}`);
      }
    } catch (error) {
      alert(`生成图表失败：${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const formatTimestamp = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  return (
    <div className={`h-full flex flex-col ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : ''}`}>
      {/* 连接状态指示器 */}
      <div className="px-6 py-3 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className={`text-sm font-medium ${isConnected ? 'text-green-700' : 'text-red-700'}`}>
              {isConnected ? '数据库已连接' : '数据库未连接'}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={checkDatabaseConnection}
              className="text-sm text-blue-600 hover:text-blue-700"
            >
              刷新状态
            </button>
            {queryResult && (
              <button
                onClick={toggleFullscreen}
                className="text-sm text-gray-600 hover:text-gray-700 flex items-center space-x-1"
                title={isFullscreen ? '恢复布局' : '全屏显示'}
              >
                {isFullscreen ? (
                  <>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                    </svg>
                    <span>恢复</span>
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                    </svg>
                    <span>全屏</span>
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* 左侧聊天区域 - 调整宽度比例 */}
        <div className={`flex-1 flex flex-col ${isFullscreen ? 'lg:max-w-1/2' : 'lg:max-w-3/5 md:max-w-full'}`}>
          {/* 聊天消息区域 */}
          <div 
            ref={chatContainerRef}
            className="flex-1 overflow-y-auto p-6 space-y-4"
            style={{ maxHeight: 'calc(100vh - 250px)' }}
          >
            {messages.length === 0 ? (
              <div className="text-center text-gray-500 mt-8">
                <div className="mb-4">
                  <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2V7a2 2 0 012-2h2a2 2 0 002 2v2a2 2 0 002 2h2a2 2 0 012-2V7a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 00-2 2h-2a2 2 0 00-2 2v6a2 2 0 01-2 2H9z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium">DB智能助手</h3>
                <p className="mt-2 max-w-md mx-auto">
                  描述您想要查询的数据需求，我会帮您生成相应的SQL语句并创建可视化图表
                </p>
                <div className="mt-4 flex flex-wrap gap-2 justify-center">
                  <span className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">
                    示例：查询销售额最高的前10个产品
                  </span>
                  <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs">
                    示例：统计各个月份的订单数量
                  </span>
                  <span className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-xs">
                    示例：分析用户年龄分布
                  </span>
                </div>
              </div>
            ) : (
              messages.map((message, index) => (
                <div
                  key={index}
                  className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-xl px-4 py-3 rounded-lg shadow-sm ${
                      message.role === 'user'
                        ? 'bg-blue-600 text-white ml-12'
                        : message.role === 'system'
                        ? 'bg-yellow-50 text-yellow-800 border border-yellow-200 mr-12'
                        : 'bg-white text-gray-900 border border-gray-200 mr-12'
                    }`}
                  >
                    <div className="text-sm leading-relaxed">
                      {message.content}
                    </div>
                    <div className={`text-xs mt-2 ${
                      message.role === 'user' ? 'text-blue-100' : 'text-gray-500'
                    }`}>
                      {formatTimestamp(message.timestamp)}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* 优化的输入区域 - 内置发送按钮 */}
          <div className="p-4 border-t border-gray-200">
            <form onSubmit={handleSubmit} className="relative">
              <div className="flex items-center bg-white border border-gray-200 rounded-full px-4 py-3 shadow-sm focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 transition-all duration-200">
                <input
                  type="text"
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  placeholder={isConnected ? "描述您的数据查询需求..." : "请先连接数据库"}
                  disabled={!isConnected || isLoading}
                  className="flex-1 px-2 py-1 bg-transparent text-gray-900 placeholder-gray-500 focus:outline-none"
                />
                
                <button
                  type="submit"
                  disabled={!isConnected || isLoading || !inputMessage.trim()}
                  className={`ml-2 w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 ${
                    !isConnected || isLoading || !inputMessage.trim()
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-blue-500 hover:bg-blue-600 text-white'
                  }`}
                  title="发送"
                >
                  {isLoading ? (
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
                  ) : (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>

        {/* 右侧SQL和结果区域 - 增加宽度 */}
        <div className={`${isFullscreen ? 'w-1/2' : 'w-2/5'} min-w-96 border-l border-gray-200 flex flex-col`}>
          {/* SQL区域 - 增加高度 */}
          {currentSql && (
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold text-gray-900">生成的SQL</h3>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setSqlEditMode(!sqlEditMode)}
                    className="text-sm text-blue-600 hover:text-blue-700"
                  >
                    {sqlEditMode ? '完成编辑' : '编辑'}
                  </button>
                  {currentSql.executed && (
                    <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                      已执行
                    </span>
                  )}
                </div>
              </div>

              {sqlEditMode ? (
                <textarea
                  value={editedSql}
                  onChange={(e) => setEditedSql(e.target.value)}
                  className="w-full h-40 p-3 text-sm font-mono border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="编辑SQL语句..."
                />
              ) : (
                <pre className="w-full h-40 p-3 text-sm font-mono bg-gray-50 border border-gray-300 rounded-md overflow-auto">
                  {editedSql}
                </pre>
              )}

              <div className="mt-3 flex space-x-2">
                <button
                  onClick={executeSql}
                  disabled={isLoading || !editedSql.trim()}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1M5 12a7 7 0 1014 0A7 7 0 005 12z" />
                  </svg>
                  <span>执行SQL</span>
                </button>
              </div>
            </div>
          )}

          {/* 查询结果区域 */}
          {queryResult && (
            <div className="flex-1 flex flex-col">
              {/* 查询结果头部 */}
              <div className="p-4 border-b border-gray-200">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-semibold text-gray-900">查询结果</h3>
                </div>
                
                {queryResult.success && (
                  <div className="text-sm text-gray-600">
                    返回 {queryResult.row_count} 行数据，耗时 {queryResult.execution_time}ms
                  </div>
                )}
              </div>

              {/* 查询结果内容区域 - 带滚动条 */}
              <div className="flex-1 p-4 overflow-y-auto" style={{ maxHeight: 'calc(100vh - 550px)' }}>
                {queryResult.success ? (
                  <>
                    {/* 图表生成按钮 */}
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">生成图表</h4>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => generateChart('bar')}
                          disabled={isLoading}
                          className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 disabled:opacity-50"
                        >
                          柱状图
                        </button>
                        <button
                          onClick={() => generateChart('line')}
                          disabled={isLoading}
                          className="px-3 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200 disabled:opacity-50"
                        >
                          折线图
                        </button>
                        <button
                          onClick={() => generateChart('pie')}
                          disabled={isLoading}
                          className="px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded hover:bg-purple-200 disabled:opacity-50"
                        >
                          饼图
                        </button>
                      </div>
                    </div>

                    {/* 图表显示 */}
                    {chartData && chartData.chart_image && (
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">数据图表</h4>
                        <div className="border border-gray-200 rounded-lg p-2 bg-white">
                          <Image
                            src={`data:image/png;base64,${chartData.chart_image}`}
                            alt="数据图表"
                            width={500}
                            height={300}
                            className="w-full h-auto rounded"
                          />
                        </div>
                      </div>
                    )}

                    {/* 数据表格 */}
                    {queryResult.data.length > 0 && (
                      <div className="overflow-x-auto">
                        <table className="min-w-full text-xs border border-gray-200">
                          <thead className="bg-gray-50 sticky top-0">
                            <tr>
                              {queryResult.columns.map((column, index) => (
                                <th
                                  key={index}
                                  className="px-2 py-1 text-left font-medium text-gray-700 border-b border-gray-200"
                                >
                                  {column}
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody>
                            {queryResult.data.slice(0, 100).map((row, rowIndex) => (
                              <tr key={rowIndex} className="hover:bg-gray-50">
                                {queryResult.columns.map((column, colIndex) => (
                                  <td
                                    key={colIndex}
                                    className="px-2 py-1 border-b border-gray-200 text-gray-900"
                                  >
                                    {row[column] !== null ? String(row[column]) : '-'}
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                        {queryResult.data.length > 100 && (
                          <div className="text-xs text-gray-500 mt-2 text-center">
                            显示前100行数据，共{queryResult.row_count}行
                          </div>
                        )}
                      </div>
                    )}
                  </>
                ) : (
                  <div className="text-red-600 text-sm">
                    <strong>执行失败：</strong>{queryResult.error}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 